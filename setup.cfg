[flake8]
max-line-length=120
exclude =
    config,
    .venv,
    venv,
    kontent/script/*,
    kontent/tasks/*,
    kontent/temp/*,
    kontent/learn_content/tests/*,
    kontent/assessment_database/tests/*,
    kontent/analyze/tests/*,
    kontent/cleaner/__init__.py,
    kontent/manage.py,
    kontent/gunicorn_config.py,
    kontent/cron_schedulers.py,
    kontent/learn_content/apps.py,

max-complexity = 8
ignore = E501, W503, E203, T001, B009

[coverage:run]
source=.
branch = True
omit =
    */.venv/*,
    */venv/*,
    *migrations*,
    *tests*,
    *apps.py,
    *migrations/*,
    *settings*,
    *config*,
    *tests/*,
    *urls.py,
    *wsgi/*,
    *manage.py,
    *script/*,
    *__init__.py,
    *analyze*,
    *tasks*,

[coverage:report]
fail_under = 34

[coverage:xml]
output = .reports/coverage/coverage.xml

[coverage:html]
directory = .reports/coverage/

[pytest]
addopts = --ignore=analyze --ignore=tasks
