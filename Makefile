clean: ## remove test and coverage artifacts
	rm -f .coverage
	rm -fr .reports/
	rm -fr .pytest_cache/

code-convention:
	export DJANGO_SETTINGS_MODULE=kontent.config.settings
	mkdir -p .reports
	pip install ruff
	ruff check kontent -v --output-file=.reports/output_flake.txt || true

test-cov:
	mkdir -p .reports
	export PYTHONPATH=""$(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))/kontent
	coverage run -m pytest --ignore=kontent/analyze --ignore=kontent/tasks kontent -v --junitxml=.reports/xunit/xunit.xml
	coverage report
	coverage xml
	export ENVIRONMENT_TEST=false

sonar: 
	docker run \
		--rm \
		-e SONAR_HOST_URL="https://sonar.keepsdev.com" \
		-e SONAR_SCANNER_OPTS="-Dsonar.projectKey=keeps-kontent-server" \
		-e SONAR_TOKEN="${SONAR_TOKEN}" \
		-v "${PWD}/:/usr/src" \
		sonarsource/sonar-scanner-cli

default_target: code-convention

all: clean code-convention test-cov