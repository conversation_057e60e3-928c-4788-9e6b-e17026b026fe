#!/bin/bash

### Local ###
export DATABASE_NAME=kontent_dev_db
export DATABASE_USER=postgres
export DATABASE_PASSWORD=postgres
export DATABASE_HOST=localhost
export DATABASE_PORT=15432

export ELASTICSEARCH_HOST=keeps.es.us-east-1.aws.found.io
export ELASTICSEARCH_INDEX=kontent-hml
export ELASTICSEARCH_PASS=[password]
export ELASTICSEARCH_URL=https://keeps.es.us-east-1.aws.found.io
export ELASTICSEARCH_USER=elastic

export PDFTRON_KEY=demo:1656284562355:7a77bd2f0300000000d2365bcd5c173dcc5f8131ef6fffb2abba5498e0

export DEBUG=True
export ENVIRONMENT=development
export TEST=True
export PYTHONPATH="${PYTHONPATH}:${PWD}/kontent/"

docker-compose up -d db

cd kontent
pytest $@
cd ..

docker-compose down
