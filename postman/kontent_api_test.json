{"info": {"_postman_id": "e3930bbb-7e79-4e04-afc3-56633158f84e", "name": "Kontent", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON>", "description": "", "item": [{"name": "Login / Token", "event": [{"listen": "test", "script": {"id": "28238de2-6689-4446-8454-49b0b6e7e023", "type": "text/javascript", "exec": ["tests[\"Login User\"] = responseCode.code === 200;", "", "var jsonData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"ACCESS_TOKEN\", jsonData['access_token']);"]}}, {"listen": "prerequest", "script": {"id": "7c6248df-c209-40a3-842f-5ca42a8f20e5", "type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "username", "value": "{{USERNAME}}", "description": "", "type": "text"}, {"key": "password", "value": "{{PASSWORD}}", "description": "", "type": "text"}]}, "url": {"raw": "{{API_MYACCOUNT_URL}}/auth", "host": ["{{API_MYACCOUNT_URL}}"], "path": ["auth"]}, "description": "Fazer o login e depois pegar o resultado, o token JWT para fazer as chamadas.\n\n<PERSON><PERSON> chamadas, incluir no Header Autentication: JWT Token"}, "response": [{"id": "52044a73-8cd4-4da4-a775-dc8cccd748e5", "name": "Login / Token", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"password\": \"{{USER_ADMIN}}\",\n  \"username\": \"{{PASS_ADMIN}}\"\n}"}, "url": {"raw": "{{API_URL}}/admin/login", "host": ["{{API_URL}}"], "path": ["admin", "login"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Length", "value": "193", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Wed, 04 Apr 2018 19:59:12 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Server", "value": "Werkzeug/0.11.1 Python/2.7.12", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\n  \"access_token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZGVudGl0eSI6MSwiaWF0IjoxNTIyODcxOTUyLCJuYmYiOjE1MjI4NzE5NTIsImV4cCI6MTUyMzI3MTk1Mn0.0cewdqPw75MawY4sTcwQfgIqhADzTCJyhXLFcIi9CZA\"\n}\n"}]}]}, {"name": "Content Category", "description": null, "item": [{"name": "List Categories", "event": [{"listen": "test", "script": {"id": "191f9aff-21c3-430d-9591-fc2da7f3fc0e", "type": "text/javascript", "exec": ["tests[\"list categories\"] = responseCode.code === 200;"]}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/learn-content/categories", "host": ["{{API_URL}}"], "path": ["learn-content", "categories"]}}, "response": []}, {"name": "Create Category", "event": [{"listen": "prerequest", "script": {"id": "d50fce78-9532-4f58-97a2-153a8d6ecae3", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "98b53abd-da27-4257-831d-89d09c242f65", "type": "text/javascript", "exec": ["tests[\"Create new category\"] = responseCode.code === 201;", "var jsonData = JSON.parse(responseBody);", "", "postman.setEnvironmentVariable(\"CATEGORY_UUID\", jsonData['id']);"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Category\",\n  \"description\": \"Description\"\n}"}, "url": {"raw": "{{API_URL}}/learn-content/categories", "host": ["{{API_URL}}"], "path": ["learn-content", "categories"]}}, "response": []}, {"name": "Update Category", "event": [{"listen": "prerequest", "script": {"id": "fc1568e5-dfc3-40a1-be78-3fb45535da80", "type": "text/javascript", "exec": ["", "var pulse = \"PULSE-TYPE-\" + Math.floor(Math.random() * (1000000 - 1 + 1) + 1)", "postman.setEnvironmentVariable(\"PULSE_TYPE_NAME\", pulse);"]}}, {"listen": "test", "script": {"id": "6cfe204e-cd9a-4417-8de5-0db498f85869", "type": "text/javascript", "exec": ["tests[\"Update category\"] = responseCode.code === 200;"]}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Name Category\",\n  \"description\": \"Description\"\n}"}, "url": {"raw": "{{API_URL}}/learn-content/categories/{{CATEGORY_UUID}}", "host": ["{{API_URL}}"], "path": ["learn-content", "categories", "{{CATEGORY_UUID}}"]}}, "response": []}, {"name": "Get Category Detail", "event": [{"listen": "test", "script": {"id": "902b284e-a9ee-41a3-b1b6-126b00eb73d5", "type": "text/javascript", "exec": ["tests[\"Get category detail\"] = responseCode.code === 200;"]}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/learn-content/categories/{{CATEGORY_UUID}}", "host": ["{{API_URL}}"], "path": ["learn-content", "categories", "{{CATEGORY_UUID}}"]}}, "response": []}, {"name": "Delete Category", "event": [{"listen": "test", "script": {"id": "7a279a34-a7d4-436e-9210-1648832de7a8", "type": "text/javascript", "exec": ["tests[\"Delete category\"] = responseCode.code === 204;"]}}], "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/learn-content/categories/{{CATEGORY_UUID}}", "host": ["{{API_URL}}"], "path": ["learn-content", "categories", "{{CATEGORY_UUID}}"]}}, "response": []}]}, {"name": "Content Point Rule", "description": null, "item": [{"name": "requirements", "description": "", "item": [{"name": "Create Category", "event": [{"listen": "prerequest", "script": {"id": "d50fce78-9532-4f58-97a2-153a8d6ecae3", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "98b53abd-da27-4257-831d-89d09c242f65", "type": "text/javascript", "exec": ["tests[\"Create new category\"] = responseCode.code === 201;", "var jsonData = JSON.parse(responseBody);", "", "postman.setEnvironmentVariable(\"CATEGORY_UUID\", jsonData['id']);"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Category\",\n  \"description\": \"Description\"\n}"}, "url": {"raw": "{{API_URL}}/learn-content/categories", "host": ["{{API_URL}}"], "path": ["learn-content", "categories"]}}, "response": []}], "_postman_isSubFolder": true}, {"name": "List Point Rules", "event": [{"listen": "test", "script": {"id": "191f9aff-21c3-430d-9591-fc2da7f3fc0e", "type": "text/javascript", "exec": ["tests[\"list categories\"] = responseCode.code === 200;"]}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/learn-content/point-rules", "host": ["{{API_URL}}"], "path": ["learn-content", "point-rules"]}}, "response": []}, {"name": "Create Point Rule", "event": [{"listen": "prerequest", "script": {"id": "d50fce78-9532-4f58-97a2-153a8d6ecae3", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "224fc4bb-05f8-49cc-9a98-8d57e76d37b5", "type": "text/javascript", "exec": ["tests[\"Create new point rule\"] = responseCode.code === 201;", "var jsonData = JSON.parse(responseBody);", "", "postman.setEnvironmentVariable(\"POINT_RULE_UUID\", jsonData['id']);"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n  \"points\": 10,\n  \"unit\": \"unit\",\n  \"content_category\": \"{{CATEGORY_UUID}}\"\n}"}, "url": {"raw": "{{API_URL}}/learn-content/point-rules", "host": ["{{API_URL}}"], "path": ["learn-content", "point-rules"]}}, "response": []}, {"name": "Update Point Rule", "event": [{"listen": "prerequest", "script": {"id": "fc1568e5-dfc3-40a1-be78-3fb45535da80", "type": "text/javascript", "exec": ["", "var pulse = \"PULSE-TYPE-\" + Math.floor(Math.random() * (1000000 - 1 + 1) + 1)", "postman.setEnvironmentVariable(\"PULSE_TYPE_NAME\", pulse);"]}}, {"listen": "test", "script": {"id": "70f5b860-9cb1-4b8a-b8d5-************", "type": "text/javascript", "exec": ["tests[\"Update point rule\"] = responseCode.code === 200;"]}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n  \"points\": 100,\n  \"unit\": \"unit\",\n  \"content_category\": \"{{CATEGORY_UUID}}\"\n}"}, "url": {"raw": "{{API_URL}}/learn-content/point-rules/{{POINT_RULE_UUID}}", "host": ["{{API_URL}}"], "path": ["learn-content", "point-rules", "{{POINT_RULE_UUID}}"]}}, "response": []}, {"name": "Get Point Rule Detail", "event": [{"listen": "test", "script": {"id": "3e4cf9e1-a98a-4cd3-a9e1-f873336588ab", "type": "text/javascript", "exec": ["tests[\"Get point detail\"] = responseCode.code === 200;"]}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/learn-content/point-rules/{{POINT_RULE_UUID}}", "host": ["{{API_URL}}"], "path": ["learn-content", "point-rules", "{{POINT_RULE_UUID}}"]}}, "response": []}, {"name": "Delete Point Rule", "event": [{"listen": "test", "script": {"id": "4692c7f4-9363-4133-a125-753b7bea5057", "type": "text/javascript", "exec": ["tests[\"Delete point rule\"] = responseCode.code === 204;"]}}], "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/learn-content/point-rules/{{POINT_RULE_UUID}}", "host": ["{{API_URL}}"], "path": ["learn-content", "point-rules", "{{POINT_RULE_UUID}}"]}}, "response": []}, {"name": "Delete Category", "event": [{"listen": "test", "script": {"id": "7a279a34-a7d4-436e-9210-1648832de7a8", "type": "text/javascript", "exec": ["tests[\"Delete category\"] = responseCode.code === 204;"]}}], "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/learn-content/categories/{{CATEGORY_UUID}}", "host": ["{{API_URL}}"], "path": ["learn-content", "categories", "{{CATEGORY_UUID}}"]}}, "response": []}]}, {"name": "Learn Content", "description": null, "item": [{"name": "requirements", "description": "", "item": [{"name": "Create Category", "event": [{"listen": "prerequest", "script": {"id": "d50fce78-9532-4f58-97a2-153a8d6ecae3", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "98b53abd-da27-4257-831d-89d09c242f65", "type": "text/javascript", "exec": ["tests[\"Create new category\"] = responseCode.code === 201;", "var jsonData = JSON.parse(responseBody);", "", "postman.setEnvironmentVariable(\"CATEGORY_UUID\", jsonData['id']);"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Category\",\n  \"description\": \"Description\"\n}"}, "url": {"raw": "{{API_URL}}/learn-content/categories", "host": ["{{API_URL}}"], "path": ["learn-content", "categories"]}}, "response": []}], "_postman_isSubFolder": true}, {"name": "List Learn Contents", "event": [{"listen": "test", "script": {"id": "c4a31930-063b-4f10-b477-7e2879e85249", "type": "text/javascript", "exec": ["tests[\"list contents\"] = responseCode.code === 200;"]}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/learn-content", "host": ["{{API_URL}}"], "path": ["learn-content"]}}, "response": []}, {"name": "Create Learn Content", "event": [{"listen": "prerequest", "script": {"id": "d50fce78-9532-4f58-97a2-153a8d6ecae3", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "bde5f0d3-6008-439d-a4c1-cb962caad90b", "type": "text/javascript", "exec": ["tests[\"Create new learn content\"] = responseCode.code === 201;", "var jsonData = JSON.parse(responseBody);", "", "postman.setEnvironmentVariable(\"LEARN_CONTENT_UUID\", jsonData['id']);"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Content Name\",\n  \"description\": \"Content Description\",\n  \"duration\": 10,\n  \"points\": 10,\n  \"content\": \"Long text\",\n  \"tags\": \"key1, key2\",\n  \"url\": \"http://keepsdev.com\",\n  \"category\": \"{{CATEGORY_UUID}}\"\n}"}, "url": {"raw": "{{API_URL}}/learn-content", "host": ["{{API_URL}}"], "path": ["learn-content"]}}, "response": []}, {"name": "Update Learn Content", "event": [{"listen": "prerequest", "script": {"id": "fc1568e5-dfc3-40a1-be78-3fb45535da80", "type": "text/javascript", "exec": ["", "var pulse = \"PULSE-TYPE-\" + Math.floor(Math.random() * (1000000 - 1 + 1) + 1)", "postman.setEnvironmentVariable(\"PULSE_TYPE_NAME\", pulse);"]}}, {"listen": "test", "script": {"id": "aac9e974-419e-4468-95a3-19b470128f21", "type": "text/javascript", "exec": ["tests[\"Update learn content\"] = responseCode.code === 200;"]}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Content Name 2\",\n  \"description\": \"Content Description\",\n  \"duration\": 10,\n  \"points\": 10,\n  \"content\": \"Long text\",\n  \"tags\": \"key1, key2\",\n  \"url\": \"http://keepsdev.com\",\n  \"category\": \"{{CATEGORY_UUID}}\"\n}"}, "url": {"raw": "{{API_URL}}/learn-content/{{LEARN_CONTENT_UUID}}", "host": ["{{API_URL}}"], "path": ["learn-content", "{{LEARN_CONTENT_UUID}}"]}}, "response": []}, {"name": "Get Learn Content Detail", "event": [{"listen": "test", "script": {"id": "9b04054d-bc9e-456a-8526-2a3dd845eeca", "type": "text/javascript", "exec": ["tests[\"Get learn content detail\"] = responseCode.code === 200;"]}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/learn-content/{{LEARN_CONTENT_UUID}}", "host": ["{{API_URL}}"], "path": ["learn-content", "{{LEARN_CONTENT_UUID}}"]}}, "response": []}, {"name": "Delete Learn Content", "event": [{"listen": "test", "script": {"id": "43bcad1a-9a5a-418b-bfaf-611d7d555987", "type": "text/javascript", "exec": ["tests[\"Delete learn content\"] = responseCode.code === 204;"]}}], "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/learn-content/{{LEARN_CONTENT_UUID}}", "host": ["{{API_URL}}"], "path": ["learn-content", "{{LEARN_CONTENT_UUID}}"]}}, "response": []}, {"name": "Delete Category", "event": [{"listen": "test", "script": {"id": "7a279a34-a7d4-436e-9210-1648832de7a8", "type": "text/javascript", "exec": ["tests[\"Delete category\"] = responseCode.code === 204;"]}}], "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/learn-content/categories/{{CATEGORY_UUID}}", "host": ["{{API_URL}}"], "path": ["learn-content", "categories", "{{CATEGORY_UUID}}"]}}, "response": []}]}]}