import unittest
from unittest.mock import patch, MagicMock
from compressors.video_compressor import VideoCompressor, VideoCompressorInput

# Dummy settings for testing
dummy_settings = {
    "MAX_WIDTH": 1280,
    "MAX_HEIGHT": 720,
    "VIDEO_BITRATE": "1000k",
    "AUDIO_BITRATE": "128k",
    "CRF": 23,
    "PRESET": "medium",
    "MINIMUM_FILE_SIZE": 1000,
    "MAXIMUM_DURATION": 300,
}


class TestVideoCompressor(unittest.TestCase):

    def test_calculate_new_scale(self):
        self.assertEqual(
            VideoCompressor.calculate_new_scale(1920, 1080, 1280, 720),
            (1280, 720)
        )
        self.assertEqual(
            VideoCompressor.calculate_new_scale(1920, 1080, 1280, None),
            (1280, 720)
        )
        self.assertEqual(
            VideoCompressor.calculate_new_scale(1920, 1080, None, 720),
            (1280, 720)
        )
        self.assertEqual(
            VideoCompressor.calculate_new_scale(1920, 1080),
            (1920, 1080)
        )

    @patch('subprocess.run')
    def test_extract_video_dimensions(self, mock_run):
        mock_run.return_value = MagicMock(
            stderr="Stream #0:0: Video: h264, yuv420p, 1920x1080, 30 fps, ...")
        dims = VideoCompressor.extract_video_dimensions("dummy.mp4")
        self.assertEqual(dims, (1920, 1080))

        mock_run.return_value = MagicMock(stderr="No video stream found")
        with self.assertRaises(ValueError):
            VideoCompressor.extract_video_dimensions("dummy.mp4")

    def test_build_ffmpeg_command(self):
        dummy_uploader = MagicMock()
        vc = VideoCompressor(uploader=dummy_uploader, settings=dummy_settings)
        command = vc.build_ffmpeg_command("input.mp4", "output.mp4", 1280, 720)
        self.assertIn('-vf', command)
        self.assertIn('scale=1280:720', command)
        command_no_scale = vc.build_ffmpeg_command("input.mp4", "output.mp4", None, None)
        self.assertNotIn('-vf', command_no_scale)

    @patch('subprocess.run')
    @patch.object(VideoCompressor, 'extract_video_dimensions', return_value=(1920, 1080))
    def test_compress_file(self, mock_extract, mock_run):
        dummy_uploader = MagicMock()
        vc = VideoCompressor(uploader=dummy_uploader, settings=dummy_settings)
        output = vc.compress_file("input.mp4")
        self.assertEqual(output, "input_compressed.mp4")
        mock_run.assert_called()

    @patch('os.remove')
    @patch('compressors.video_compressor.get_file_size')
    @patch.object(VideoCompressor, 'compress_file', return_value="compressed_video.mp4")
    def test_compress_content(self, mock_compress_file, mock_get_file_size, mock_remove):
        dummy_uploader = MagicMock()
        dummy_uploader.extract_s3_url_bucket_key.return_value = ("dummy-bucket", "general/video.mp4")
        dummy_uploader.send_file.return_value = {
            "url": "https://s3.keepsdev.com/clients/dummy/general/compressed/video.mp4"
        }

        vc = VideoCompressor(uploader=dummy_uploader, settings=dummy_settings)

        # Case 1: Unsupported MIME type; compression is skipped.
        input_data = VideoCompressorInput(
            file_path="input.mp4",
            actual_file_url="https://original.url/video.mp4",
            file_mime_type="application/pdf",
            duration=100
        )
        mock_get_file_size.return_value = 5000
        url, size = vc.compress_content(input_data)
        self.assertEqual(url, "https://original.url/video.mp4")
        self.assertEqual(size, 5000)

        # Case 2: Duration exceeds maximum; compression is skipped.
        input_data = VideoCompressorInput(
            file_path="input.mp4",
            actual_file_url="https://original.url/video.mp4",
            file_mime_type="video/mp4",
            duration=400
        )
        mock_get_file_size.return_value = 5000
        url, size = vc.compress_content(input_data)
        self.assertEqual(url, "https://original.url/video.mp4")
        self.assertEqual(size, 5000)

        # Case 3: Valid compression.
        input_data = VideoCompressorInput(
            file_path="input.mp4",
            actual_file_url="https://original.url/video.mp4",
            file_mime_type="video/mp4",
            duration=100
        )

        # Define different file sizes for the original and compressed files.
        def get_size_side_effect(file_path):
            if file_path == "input.mp4":
                return 5000
            elif file_path == "compressed_video.mp4":
                return 3000
            return 0

        mock_get_file_size.side_effect = get_size_side_effect

        url, size = vc.compress_content(input_data)
        mock_remove.assert_called_with("input.mp4")
        self.assertEqual(
            url,
            "https://s3.keepsdev.com/clients/dummy/general/compressed/video.mp4"
        )
        self.assertEqual(size, 3000)

