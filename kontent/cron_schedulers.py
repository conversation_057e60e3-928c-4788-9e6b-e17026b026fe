"""
Scheduler configured to run with supervisor

See .ebextensions/99.celery.config
"""
import datetime
from apscheduler.schedulers.blocking import BlockingScheduler

from tasks import scheduler


def health_check():
    print("{} Schedulers alive".format(datetime.datetime.now()))


if __name__ == '__main__':
    print('Running schedulers...')
    scheduler.start_background_schedulers()
    lock_scheduler = BlockingScheduler(timezone="America/Sao_Paulo")
    lock_scheduler.add_job(health_check, trigger='interval', minutes=60)
    lock_scheduler.start()
