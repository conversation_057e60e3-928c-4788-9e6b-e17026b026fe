from analyze import AnalyzeVideoAudioLink
from learn_content.models import Learn<PERSON>ontent
from tasks.content_analyzers.update_doc import update_elastic_doc


def analyze_audio_video_link(content: LearnContent):
    analyzer = AnalyzeVideoAudioLink()
    try:
        result = analyzer.upload_file_to_transcript(content.url)
        content.transcribe_job = result.get('job')
        content.analyzed = result.get('analyzed')
        content.save()
        update_elastic_doc(content.id, result)
    except Exception as error:
        analyzer.logger('link_audio_video_process', f'error: {str(error)}, content: {content.id}')
        content.analyzed = True
        content.save()
        update_elastic_doc(content.id, {"duration": 0, "points": 0, "analyzed": True})
