from injector import inject

from base_task import BaseTask
from config.celery import app
from file_types import PDF, MEDIA_FILE, MEDIA_LINK, GOOGLE_DRIVE, MSOFFICE_FILE, SCORM, <PERSON>MAGE
from learn_content.models import LearnContent
from task_names import ANALYZE_CONTENT
from tasks.content_analyzers.analyze_drive_file import analyze_drive_file
from tasks.content_analyzers.analyze_image_file import analyze_image_file
from tasks.content_analyzers.analyze_media_file import analyze_media_file
from tasks.content_analyzers.analyze_audio_video_link import analyze_audio_video_link
from tasks.content_analyzers.analyze_pdf import analyze_pdf
from tasks.content_analyzers.analyze_ms_office_file import analyze_ms_office_file
from tasks.content_analyzers.analyze_scorm import analyze_scorm_file

ANALYZE_TASKS = {
    PDF: analyze_pdf,
    SCORM: analyze_scorm_file,
    IMAGE: analyze_image_file,
    MEDIA_FILE: analyze_media_file,
    MEDIA_LINK: analyze_audio_video_link,
    GOOGLE_DRIVE: analyze_drive_file,
    MSOFFICE_FILE: analyze_ms_office_file,
}


@app.task(name=ANALYZE_CONTENT, base=BaseTask, track_started=True)
def analyze_content(content_id: str):
    content = LearnContent.objects.get(id=content_id)
    ANALYZE_TASKS[content.file_type](content)
