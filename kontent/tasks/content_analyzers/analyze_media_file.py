from analyze import AnalyzeVideoAudio
from di import Container
from learn_content.models import Learn<PERSON>ontent, ContentPointRule
from tasks.content_analyzers.update_doc import update_elastic_doc


def analyze_media_file(content: LearnContent):
    di = Container()
    s3_client = di.aws_s3_client()
    content_point = ContentPointRule.objects.filter(content_type=content.content_type).first()
    bucket, key = s3_client.extract_s3_url_bucket_key(content.url)
    file_path = s3_client.download(key, bucket)

    analyzer = AnalyzeVideoAudio(
        file_name=None,
        points_rules=content_point.points,
        points_quantity=content_point.quantity,
        file_path=file_path
    )

    try:
        result = analyzer.transcribe(content.transcribe_job)

        if result.get('analyzed') is True:
            content.analyzed = True
            content.transcribe_job = None
            content.save()
            update_elastic_doc(content.id, result)
    except Exception as error:
        analyzer.logger('transcribe_process', f'error: {str(error)}, content: {content.id}')
        content.analyzed = True
        content.save()
        update_elastic_doc(content.id, {"duration": 0, "points": 0, "analyzed": True})
