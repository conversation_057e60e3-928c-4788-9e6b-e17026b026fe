from analyze import AnalyzeGdrive
from learn_content.models import <PERSON><PERSON><PERSON>ontent, ContentPointRule
from tasks.content_analyzers.update_doc import update_elastic_doc


def analyze_drive_file(content: LearnContent):
    content_point = ContentPointRule.objects.filter(content_type=content.content_type).first()
    analyzer = AnalyzeGdrive(points_rules=content_point.points, points_quantity=content_point.quantity)
    try:
        result = analyzer.async_analyzer(content.url)
        content.analyzed = result.get('analyzed')
        content.save()
        update_elastic_doc(content.id, result)
    except Exception as error:
        analyzer.logger('gdrive_async_analyzer', f'error: {str(error)}, content: {content.id}')
        content.analyzed = True
        content.save()
        update_elastic_doc(content.id, {"duration": 0, "points": 0, "analyzed": True})
