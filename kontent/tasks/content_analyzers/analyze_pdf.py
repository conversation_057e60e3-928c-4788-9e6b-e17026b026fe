import os

from analyze import Analyze<PERSON><PERSON>
from config.settings import PDF_TYPE
from di import Container
from learn_content.models import Learn<PERSON>ontent, ContentPointRule
from tasks.content_analyzers.update_doc import update_elastic_doc


def analyze_pdf(content: LearnContent):
    di = Container()
    s3_client = di.aws_s3_client()
    content_point = ContentPointRule.objects.filter(content_type=content.content_type).first()
    analyzer = AnalyzePDF(
        file_name=None,
        points_rules=content_point.points,
        points_quantity=content_point.quantity,
        file_path=None
    )
    bucket, key = s3_client.extract_s3_url_bucket_key(content.url)
    analyzer.file_to_analyze = s3_client.download(key, bucket)

    try:
        analyzer.compress(content.url)
    except Exception as error:
        analyzer.logger('pdf_compress_error', f'error: {str(error)}, content: {content.id}')

    try:
        result = analyzer.process()
    except Exception as error:
        analyzer.logger('pdf_process_error', f'error: {str(error)}, content: {content.id}')
        content.analyzed = True
        content.save()
        update_elastic_doc(content.id, {"duration": 0, "points": 0, "analyzed": True})
        return

    content.analyzed = result.get('analyzed')
    content.save()
    update_elastic_doc(content.id, result)
    os.remove(analyzer.file_to_analyze)
