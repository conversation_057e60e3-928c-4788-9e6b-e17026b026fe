# -*- coding: utf-8 -*-
from elasticsearch_dsl.connections import connections
import datetime

from analyze import AnalyzeVideoAudio
from cleaner.query_selector import QuerySelector
from config.settings import DATABASES, CLEANER_QUERIES_DIRECTORY
from cleaner.cleaner_service import CleanerService
from cleaner.database import create_engine_and_session
from learn_content.models import LearnContent
from tasks.content_analyzers.update_doc import update_elastic_doc


def transcribe_process():
    print(f"{datetime.datetime.now()} | transcribe_process")
    analyzer = AnalyzeVideoAudio()
    transcribe_contents = LearnContent.objects.filter(analyzed=False, transcribe_job__isnull=False).all()
    for content in transcribe_contents:
        try:
            print(f"{datetime.datetime.now()} | Processing transcribe content id {content.id}")
            result = analyzer.transcribe(content.transcribe_job)
            if result.get('analyzed') is True:
                content.analyzed = True
                content.transcribe_job = None
                content.save()
                update_elastic_doc(content.id, result)
        except Exception as error:
            analyzer.logger('transcribe_process', f'error: {str(error)}, content: {content.id}')
            content.analyzed = True
            content.save()
            update_elastic_doc(content.id, {"duration": 0, "points": 0, "analyzed": True})


def clean_elastic_search_index():
    database = DATABASES['default']
    database_url = f"postgresql://{database['USER']}:{database['PASSWORD']}@{database['HOST']}:{database['PORT']}/{database['NAME']}"
    connection = connections.get_connection()
    engine, _ = create_engine_and_session(database_url)
    query_selector = QuerySelector(engine, CLEANER_QUERIES_DIRECTORY)
    CleanerService(query_selector, connection).clean_elastic_search()
