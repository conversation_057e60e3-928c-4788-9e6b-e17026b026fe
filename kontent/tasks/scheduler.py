# -*- coding: utf-8 -*-
from apscheduler.schedulers.background import BackgroundScheduler
from config import settings
from tasks.analyzer import transcribe_process, clean_elastic_search_index


def start_background_schedulers():
    jobs_interval = [
        {'job': transcribe_process, 'trigger': 'interval', 'minutes': int(settings.SCHEDULER_TRANSCRIBE_PROCESS_INTERVAL)},
        {'job': clean_elastic_search_index, 'trigger': 'interval', 'minutes': int(settings.SCHEDULER_CLEAN_ELASTIC_SEARCH_INTERVAL)},
    ]

    scheduler = BackgroundScheduler(timezone="America/Sao_Paulo")

    for j in jobs_interval:
        scheduler.add_job(j['job'], trigger=j['trigger'], minutes=j['minutes'])

    scheduler.start()
