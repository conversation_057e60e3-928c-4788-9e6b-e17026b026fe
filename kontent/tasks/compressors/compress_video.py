from celery.utils.log import get_task_logger

from compressors.video_compressor import VideoCompressorInput
from di import Container
from learn_content.models import LearnContent
from tasks.content_analyzers.update_doc import update_elastic_doc

logger = get_task_logger(__name__)


def compress_video(content: LearnContent):
    di = Container()
    s3_client = di.aws_s3_client()
    compressor = di.video_compressor()
    file_path = s3_client.download_by_url(content.url)

    compressor_input = VideoCompressorInput(
        file_path=file_path,
        actual_file_url=content.url,
        file_mime_type=content.file_mime_type,
        duration=content.duration
    )

    try:
        new_url, file_size = compressor.compress_content(compressor_input)
    except Exception as error:
        logger.error(f'Video Compressor Execution - {str(error)}, content: {content.id}')
        raise error

    content.file_size = file_size
    content.compressed = True
    content.url = new_url
    content.save()
    update_elastic_doc(content.id, {"file_size": file_size, "compressed": True, "url": new_url})
