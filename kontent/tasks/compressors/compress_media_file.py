from base_task import BaseTask
from config.celery import app
from learn_content.models import LearnContent
from task_names import COMPRESS_CONTENT
from tasks.compressors.compress_video import compress_video
from tasks.content_analyzers.update_doc import update_elastic_doc

COMPRESS_TASKS = {
    compress_video: ["video/3gp", "video/mp4"]
}


@app.task(name=COMPRESS_CONTENT, base=BaseTask, track_started=True)
def compress_content(content_id: str):
    content = LearnContent.objects.get(id=content_id)

    task_found = None
    for task, supported_task_mime_types in COMPRESS_TASKS.items():
        if content.file_mime_type in supported_task_mime_types:
            task_found = task
            break

    if not task_found:
        content.compressed = True
        content.save()
        update_elastic_doc(content.id, {"compressed": True})

    task_found(content)
