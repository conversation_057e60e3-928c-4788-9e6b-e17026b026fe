# -*- coding: utf-8 -*-
import os
import mock
from django.test import TestCase

from analyze.tests.mock_fixtures import language_aws_comprehend, entity_aws_comprehend, keys_aws_comprehend
from learn_content.models import LearnContent
from tasks.content_analyzers.analyze_ms_office_file import analyze_ms_office_file
from tasks.tests.mock_fixtures import XLSX_DOC


@mock.patch('utils.google.gdrive.GoogleDrive.send_file', return_value={"url": "google_drive_url"})
@mock.patch('utils.aws.aws_comprehend.AwsComprehendClient.dominant_language', return_value=language_aws_comprehend)
@mock.patch('utils.aws.aws_comprehend.AwsComprehendClient.entities', return_value=entity_aws_comprehend)
@mock.patch('utils.aws.aws_comprehend.AwsComprehendClient.key_phrases', return_value=keys_aws_comprehend)
@mock.patch('tasks.content_analyzers.update_doc.update_elastic_doc', return_value={})
class MsOfficeTaskAnalyzerTestCase(TestCase):
    fixtures = ['content_type', 'content_point_rule']

    def setUp(self):
        self._base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    @mock.patch("utils.aws.AmazonS3.download")
    def test_process_msoffice_with_error(self, download, *args):
        instance = LearnContent()
        instance.analyzed = False
        instance.url = 'https://aws.bucket.comt/files/file_name.xlsx'
        instance.content_type_id = '673e4c02-ae1c-4e61-830b-706d35bd0b11'
        instance.save()
        analyze_ms_office_file(instance)
        args[0].assert_called_with(instance.id, {'duration': 0, 'points': 0, 'analyzed': True})

    @mock.patch("utils.aws.AmazonS3.download")
    def test_process_msoffice_xlsx(self, download, *args):
        download.return_value = os.path.join(self._base_dir, 'tests/files/sample.xlsx')
        instance = LearnContent()
        instance.analyzed = False
        instance.url = 'https://aws.bucket.comt/files/file_name.xlsx'
        instance.content_type_id = '673e4c02-ae1c-4e61-830b-706d35bd0b11'
        instance.name = 'file_name'

        instance.save()

        analyze_ms_office_file(instance)

        args[0].assert_called_with(instance.id, XLSX_DOC)
