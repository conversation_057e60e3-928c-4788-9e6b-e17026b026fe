import os

import mock
from django.test import TestCase

from analyze.tests.mock_fixtures import language_aws_comprehend, entity_aws_comprehend, keys_aws_comprehend
from learn_content.models import LearnContent
from tasks.content_analyzers.analyze_pdf import analyze_pdf
from tasks.tests.mock_fixtures import PDF_DOC


@mock.patch('utils.aws.aws_comprehend.AwsComprehendClient.dominant_language', return_value=language_aws_comprehend)
@mock.patch('utils.aws.aws_comprehend.AwsComprehendClient.entities', return_value=entity_aws_comprehend)
@mock.patch('utils.aws.aws_comprehend.AwsComprehendClient.key_phrases', return_value=keys_aws_comprehend)
@mock.patch('tasks.analyzer.update_elastic_pdf_doc', return_value={})
class PdfProcessTaskTestCase(TestCase):
    fixtures = ['content_type', 'content_point_rule']

    def setUp(self):
        self._base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    @staticmethod
    def test_process_pdf_with_error(update_elastic_content):
        content = LearnContent()
        content.analyzed = False
        content.url = 'https://aws.bucket.comt/files/file_name.pdf'
        content.content_type_id = '0faac34b-2393-4352-8a94-a9ee0659f824'
        analyze_pdf(content)
        update_elastic_content.assert_called_with(content.id, {'duration': 0, 'points': 0, 'analyzed': True})

    @mock.patch("utils.aws.AmazonS3.download")
    def test_process_pdf(self, download, update_elastic_content):
        download.return_value = os.path.join(self._base_dir, 'tests/files/sample.pdf')
        instance = LearnContent()
        instance.analyzed = False
        instance.url = 'https://aws.bucket.comt/files/file_name.pdf'
        instance.content_type_id = '0faac34b-2393-4352-8a94-a9ee0659f824'
        analyze_pdf(instance)

        update_elastic_content.assert_called_with(instance.id, PDF_DOC)