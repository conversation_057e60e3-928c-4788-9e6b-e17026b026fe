from django.test import TestCase
import mock
from elasticmock import elasticmock

from analyze import AnalyzeVideoAudioLink
from learn_content.models import LearnContent, LearnContentDocument
from tasks.content_analyzers.analyze_audio_video_link import analyze_audio_video_link
from utils.google import GoogleTranscribe


class TestAnalyzeAudioVideoLink(TestCase):

    @elasticmock
    @mock.patch.object(LearnContentDocument, 'save')
    @mock.patch.object(LearnContentDocument, 'get')
    @mock.patch.object(AnalyzeVideoAudioLink, 'upload_file_to_transcript')
    @mock.patch.object(GoogleTranscribe, '__init__', return_value=None)
    def test_should_analyze_video(self,  init, upload_file_to_transcript: mock.MagicMock, get: mock.MagicMock, save: mock.MagicMock):
        transcribe_result = {"job": "jobid", "analyzed": False}
        document = LearnContentDocument({"url": "tests"})
        get.return_value = document
        upload_file_to_transcript.return_value = transcribe_result
        content = LearnContent(transcribe_job="1234")

        analyze_audio_video_link(content)

        self.assertFalse(content.analyzed)
        self.assertEqual(content.transcribe_job, "jobid")
        for field in transcribe_result:
            self.assertEqual(document.__getattr__(field), transcribe_result[field])
        save.assert_called()
