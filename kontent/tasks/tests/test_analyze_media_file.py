from django.test import TestCase
import mock
from elasticmock import elasticmock

from analyze import AnalyzeVideoAudio
from learn_content.models import Learn<PERSON>ontent, LearnContentDocument
from tasks.content_analyzers.analyze_media_file import analyze_media_file


class TestAnalyzeMediaFile(TestCase):

    @elasticmock
    @mock.patch.object(LearnContentDocument, 'save')
    @mock.patch.object(LearnContentDocument, 'get')
    @mock.patch.object(AnalyzeVideoAudio, 'transcribe')
    @mock.patch.object(AnalyzeVideoAudio, '__init__', return_value=None)
    def test_should_analyze_video(self,  init: mock.MagicMock, transcribe: mock.MagicMock, get: mock.MagicMock, save: mock.MagicMock):
        transcribe_result = {"analyzed": True, "duration": 100, "points": 200}
        document = LearnContentDocument({"url": "tests"})
        get.return_value = document
        transcribe.return_value = transcribe_result
        content = LearnContent(transcribe_job="1234")

        analyze_media_file(content)

        self.assertTrue(content.analyzed)
        self.assertIsNone(content.transcribe_job)
        for field in transcribe_result:
            self.assertEqual(document.__getattr__(field), transcribe_result[field])
        save.assert_called()
