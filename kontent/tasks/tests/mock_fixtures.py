XLSX_DOC = {
  'duration': 2,
  'points': 6,
  'transcript': 'A LXP da Keeps é uma solução simples e completa de capacitação com inteligência artificial, mobilidade, gamificação e design intuitivo.',
  'entities': None,
  'language': 'pt',
  'summary': 'A LXP da Keeps é uma solução simples e completa de capacitação com inteligência artificial, mobilidade, gamificação e design intuitivo.',
  'analyzed': True
}

GDRIVE_DOC = {
  'duration': 1,
  'points': 4,
  'transcript': 'Teste Test Testando Teste. Nao Apagar. Arquivo para testes automatizados do\nkonquest.\n\n',
  'tags': None,
  'entities': None,
  'language': None,
  'summary': 'Teste Test Testando Teste.\nNao Apagar.\nArquivo para testes automatizados do konquest.',
  'analyzed': True
}

PDF_DOC = {
  'duration': 2,
  'points': 6,
  'transcript': 'A LXP da Keeps é uma solução simples e completa de capacitação com\ninteligência artificial, mobilidade, gamificação e design intuitivo.\n\n\x0c',
  'tags': [{'text': 'a lxp da keeps', 'relevance': 0.9991660118103027},
           {'text': 'uma solução simples e completa de capacitação com inteligência artificial',
             'relevance': 0.9997682571411133}, {'text': 'mobilidade', 'relevance': 0.9998513460159302},
           {'text': 'gamificação', 'relevance': 0.9997623562812805},
           {'text': 'design intuitivo', 'relevance': 0.9997573494911194}], 'entities': [], 'language': 'pt',
  'summary': None,
  'analyzed': True
}