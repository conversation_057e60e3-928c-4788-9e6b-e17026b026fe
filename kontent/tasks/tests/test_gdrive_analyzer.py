# -*- coding: utf-8 -*-
import os
import mock
from django.test import TestCase

from learn_content.models import LearnContent
from tasks.content_analyzers.analyze_drive_file import analyze_drive_file
from tasks.tests.mock_fixtures import GDRIVE_DOC


@mock.patch('tasks.content_analyzers.update_doc.update_elastic_doc', return_value={})
class GdriveAsyncTaskAnalyzerTestCase(TestCase):
    fixtures = ['content_type', 'content_point_rule']

    def setUp(self):
        self._base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    @staticmethod
    def test_process_gdrive_with_error(*args):
        instance = LearnContent()
        instance.analyzed = False
        instance.url = 'https://docs.google.com/document-xx/d/xx/edit'
        instance.content_type_id = 'b7094e27-b263-4fed-a928-6f0a78439cbe'
        instance.save()
        analyze_drive_file(instance)
        args[0].assert_called_with(instance.id, {'duration': 0, 'points': 0, 'analyzed': True})

    @staticmethod
    def test_process_gdrive_doc(*args):
        instance = LearnContent()
        instance.analyzed = False
        instance.url = 'https://docs.google.com/document/d/1_XNh1K8vSSy81rkwYdvCwqFgqLlsMrL0Wt8lbv7Kmlo/edit'
        instance.content_type_id = 'b7094e27-b263-4fed-a928-6f0a78439cbe'
        instance.save()
        analyze_drive_file(instance)
        args[0].assert_called_with(instance.id, GDRIVE_DOC)
