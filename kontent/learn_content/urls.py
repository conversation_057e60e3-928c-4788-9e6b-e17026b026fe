# -*- coding: utf-8 -*-

from django.urls import path

from learn_content.views.check_contents_analyzed_viewset import CheckContentsAnalyzedViewSet
from learn_content.views.content_category_viewset import ContentCategoryViewSet
from learn_content.views.content_point_rule_viewset import ContentPointRuleViewSet
from learn_content.views.content_type_viewset import ContentTypeViewSet
from learn_content.views.learn_content_copy_viewset import LearnContentCopyViewSet
from learn_content.views.learn_content_viewset import LearnContentViewSet, LearnScormContentViewSet
from learn_content.views.learn_content_viewset_v2 import LearnContentViewSetV2
from learn_content.views.transcribe_vocabulary_viewset import VocabularyTranscribeViewSet

_read_only = {'get': 'list'}

_read_only_detail = {'get': 'retrieve'}

_save_only = {'post': 'create'}

_list = {'get': 'list',
         'post': 'create'}

_detail = {'get': 'retrieve',
           'put': 'update',
           'patch': 'partial_update',
           'delete': 'destroy'}

urlpatterns = [
    path('', LearnContentViewSet.as_view(_list), name='learn-content-list'),
    path('/scorm', LearnScormContentViewSet.as_view({'post': 'create'}), name='learn-content-scorm'),
    path('/<uuid:pk>', LearnContentViewSet.as_view(_detail), name='learn-content-detail'),
    path('/<uuid:pk>/copy', LearnContentCopyViewSet.as_view(_save_only), name='learn-content-copy'),
    path('/categories', ContentCategoryViewSet.as_view(_list), name='learn-content-category-list'),
    path('/categories/<uuid:pk>', ContentCategoryViewSet.as_view(_detail), name='learn-content-category-detail'),
    path('/types', ContentTypeViewSet.as_view(_read_only), name='learn-content-type-list'),
    path('/types/<uuid:pk>', ContentTypeViewSet.as_view(_read_only_detail), name='learn-content-type-detail'),
    path('/point-rules', ContentPointRuleViewSet.as_view(_list), name='learn-content-point-rule-list'),
    path('/point-rules/<uuid:pk>', ContentPointRuleViewSet.as_view(_detail), name='learn-content-point-rule-detail'),
    path('/transcribe-vocabulary', VocabularyTranscribeViewSet.as_view({'post': 'create'}), name='transcribe-voc-update'),
    path('/check-contents-analyzed', CheckContentsAnalyzedViewSet.as_view({'post': 'create'}), name='check-contents-analyzed'),
    path('/v2', LearnContentViewSetV2.as_view({'post': 'create'}), name='learn-content-v2')
]
