# -*- coding: utf-8 -*-

from rest_framework import viewsets
from rest_framework.filters import <PERSON><PERSON><PERSON><PERSON>, OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend

from learn_content.models.content_type import ContentType
from learn_content.serializers import ContentTypeSerializer


# pylint: disable=arguments-differ
class ContentTypeViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    def get_queryset(self):
        return ContentType.objects.filter()

    @staticmethod
    def get_serializer_class():
        return ContentTypeSerializer
