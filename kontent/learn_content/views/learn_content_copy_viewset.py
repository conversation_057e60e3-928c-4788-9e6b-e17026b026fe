from rest_framework import viewsets, status
from rest_framework.response import Response

from learn_content.serializers import LearnContentSerializer
from learn_content.services.content_service import ContentService
from elasticsearch_dsl.connections import connections


class LearnContentCopyViewSet(viewsets.ModelViewSet):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._service = ContentService(connections.get_connection())

    def create(self, request, *args, **kwargs):
        instance = self._service.copy(kwargs['pk'])
        serializer = LearnContentSerializer(instance=instance)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
