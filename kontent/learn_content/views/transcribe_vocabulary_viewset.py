# -*- coding: utf-8 -*-
from django_injector import inject
from rest_framework import viewsets, status
from rest_framework.response import Response
from di import Container


# pylint: disable=empty-docstring
class VocabularyTranscribeViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    @inject
    def __init__(self, container: Container):
        self.container = container

    def get_queryset(self):
        pass

    def create(self, request, *args, **kwargs):
        """
        """
        data = request.data
        response = self.container.aws_transcribe_client.create_or_update_vocabulary(data['sentences'], data['vocabulary'])
        return Response(response, status=status.HTTP_201_CREATED)
