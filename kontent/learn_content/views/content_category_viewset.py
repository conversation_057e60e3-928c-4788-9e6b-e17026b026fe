# -*- coding: utf-8 -*-

from rest_framework import viewsets
from rest_framework.filters import <PERSON><PERSON><PERSON><PERSON>, OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend

from learn_content.models.content_category import ContentCategory
from learn_content.serializers import ContentCategorySerializer


# pylint: disable=arguments-differ
class ContentCategoryViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    def get_queryset(self):
        return ContentCategory.objects.filter()

    @staticmethod
    def get_serializer_class():
        return ContentCategorySerializer
