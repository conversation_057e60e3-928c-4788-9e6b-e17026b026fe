from rest_framework import viewsets, mixins
from rest_framework.response import Response
from rest_framework.status import HTTP_204_NO_CONTENT

from di import Container
from learn_content.serializers.check_contents_analyzed_serializer import CheckContentsAnalyzedSerializer


class CheckContentsAnalyzedViewSet(viewsets.GenericViewSet, mixins.CreateModelMixin):
    serializer_class = CheckContentsAnalyzedSerializer

    # todo: implementar modulos do injector para utilizar injeção de depedência
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._service = Container().check_contents_analyzed_service()

    def create(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(True)
        self._service.create(serializer.validated_data)
        return Response(status=HTTP_204_NO_CONTENT)
