# -*- coding: utf-8 -*-

from rest_framework import viewsets
from rest_framework.filters import <PERSON><PERSON><PERSON><PERSON>, OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend

from learn_content.models.content_point_rule import ContentPointRule
from learn_content.serializers import ContentPointRuleSerializer


# pylint: disable=arguments-differ
class ContentPointRuleViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ('content_type__name',)

    def get_queryset(self):
        return ContentPointRule.objects.filter()

    @staticmethod
    def get_serializer_class():
        return ContentPointRuleSerializer
