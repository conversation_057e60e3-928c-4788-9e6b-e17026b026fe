from elasticsearch_dsl.connections import connections
from rest_framework import viewsets

from learn_content.serializers.learn_content_create_serializer import LearnContentCreateSerializer
from learn_content.services.content_service import ContentService


class LearnContentViewSetV2(viewsets.GenericViewSet, viewsets.mixins.CreateModelMixin):
    serializer_class = LearnContentCreateSerializer

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # todo: implement container manager to inject dependencies
        self.service = ContentService(connections.get_connection())

    def perform_create(self, serializer: LearnContentCreateSerializer):
        workspace_id = self.request.user.get('client_id')
        self.service.create_and_analyze(
            serializer.validated_data,
            workspace_id
        )
