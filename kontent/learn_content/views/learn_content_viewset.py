# -*- coding: utf-8 -*-
import uuid
from typing import List

from elasticsearch_dsl.connections import connections
from rest_framework import viewsets, status
from rest_framework.exceptions import ValidationError
from rest_framework.filters import SearchFilter, OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.response import Response
from rest_framework.status import HTTP_204_NO_CONTENT

from learn_content.models import LearnContent
from learn_content.serializers import LearnContentSerializer, LearnContentListSerializer, ScormSerializer
from learn_content.serializers.learn_content_serializer import LearnContentUpdateSerializer
from learn_content.services.content_service import ContentService
from learn_content.services.scrom_service import ScormService


class LearnContentViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._service = ContentService(connections.get_connection())

    def get_queryset(self):
        return LearnContent.objects.filter()

    def get_serializer_class(self):
        return LearnContentListSerializer if self.request.method == 'GET' else LearnContentSerializer

    @staticmethod
    def _is_valid_ids(ids: List[str]):
        for _id in ids:
            try:
                uuid.UUID(_id)
            except ValueError as e:
                raise ValidationError(f"'{_id}' is invalid uuid") from e

    def list(self, request, *args, **kwargs):
        query_ids = self.request.query_params.get('pk')
        ids = query_ids.split(",") if query_ids else []
        self._is_valid_ids(ids)
        response_json = self._service.get_content_documents(ids)
        return Response(response_json)

    def retrieve(self, request, *args, **kwargs):
        content_uuid = str(self.kwargs.get('pk'))
        document = self._service.get_document(content_uuid)
        return Response(document)

    def update(self, request, *args, **kwargs):
        content_uuid = str(self.kwargs.get('pk'))
        serializer = LearnContentUpdateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self._service.update(content_uuid, serializer.validated_data)
        return Response(status=HTTP_204_NO_CONTENT)

    def create(self, request, *args, **kwargs):
        workspace_id = request.user.get('client_id')
        instance = self._service.create_and_analyze(request.data, workspace_id)
        serializer = LearnContentSerializer(instance=instance)
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class LearnScormContentViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._service = ScormService()

    def get_serializer_class(self):
        return LearnContentListSerializer if self.request.method == 'GET' else ScormSerializer

    def create(self, request, *args, **kwargs):
        response = self._service.process_scorm(request.data, serializer=ScormSerializer)
        steps = response.get('course_steps')
        return Response({
            "course_title": response.get('course_title'),
            "steps": steps},
            status=status.HTTP_201_CREATED)
