import uuid

from django.db import models

from learn_content.models.application_name_enum import ApplicationNameEnum


class CheckContentsAnalyzed(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    application_name = models.CharField(
        verbose_name="Application Name",
        choices=ApplicationNameEnum.choices(),
        max_length=50,
    )
    callback_id = models.CharField(verbose_name="Callback id", max_length=200)
    is_done = models.BooleanField(verbose_name="Is done?", default=False)

    class Meta:
        app_label = "learn_content"
        db_table = 'check_contents_analyzed'
