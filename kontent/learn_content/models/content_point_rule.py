# -*- coding: utf-8 -*-

import uuid

from django.db import models

from learn_content.models.content_type import ContentType


class ContentPointRule(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    content_type = models.ForeignKey(ContentType, verbose_name="Content Category",
                                     on_delete=models.PROTECT)
    points = models.IntegerField(verbose_name="Points")
    unit = models.CharField(verbose_name="Unit", max_length=100)
    quantity = models.IntegerField(verbose_name="Quantity")

    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True)

    class Meta:
        app_label = 'learn_content'
        verbose_name_plural = "Content Point Rules"
        db_table = 'content_point_rule'
