# -*- coding: utf-8 -*-

import uuid

from django.db import models


class ContentCategory(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(verbose_name="Name", max_length=200)
    description = models.TextField(verbose_name="Description", null=True, blank=True)

    image = models.URLField(verbose_name="Pulse Type Image", null=False, blank=False)

    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True)

    class Meta:
        app_label = 'learn_content'
        verbose_name_plural = "Content Categories"
        db_table = 'content_category'
