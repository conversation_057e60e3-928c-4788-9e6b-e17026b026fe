import uuid

from django.db import models

from learn_content.models import LearnContent
from learn_content.models.check_contents_analyzed import CheckContentsAnalyzed


class CheckContentsAnalyzedDetail(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    learn_content = models.ForeignKey(
        LearnContent, verbose_name='Learn content', on_delete=models.SET_NULL, null=True, blank=True
    )
    check_contents_analyzed = models.ForeignKey(
        CheckContentsAnalyzed, verbose_name='Check contents analyzed', on_delete=models.CASCADE
    )

    class Meta:
        app_label = "learn_content"
        db_table = 'check_content_analyzed_detail'
