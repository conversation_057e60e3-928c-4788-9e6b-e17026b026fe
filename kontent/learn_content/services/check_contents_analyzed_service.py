from typing import Dict, Callable

from django.db import transaction

from learn_content.models import LearnContent
from learn_content.models.check_contents_analyzed import CheckContentsAnalyzed
from learn_content.models.check_contents_analyzed_detail import CheckContentsAnalyzedDetail


class CheckContentsAnalyzedService:
    def __init__(self, callback_task_by_application: Dict[str, Callable]):
        self._callback_task_by_application = callback_task_by_application

    def create(self, data: dict) -> CheckContentsAnalyzed:
        with transaction.atomic():
            content_ids = data["learn_content_ids"]
            check = CheckContentsAnalyzed(
                application_name=data["application_name"],
                callback_id=data["callback_id"]
            )
            CheckContentsAnalyzed.objects.filter(
                application_name=check.application_name,
                callback_id=check.callback_id,
                is_done=False
            ).delete()
            check.save()

            details = []
            for content_id in content_ids:
                details.append(CheckContentsAnalyzedDetail(check_contents_analyzed=check, learn_content_id=content_id))

            CheckContentsAnalyzedDetail.objects.bulk_create(details)
        self.make_check(content_ids[0])
        return check

    def make_check(self, learn_content_id: str) -> None:
        check_ids = CheckContentsAnalyzedDetail.objects.filter(learn_content_id=learn_content_id).values_list(
            "check_contents_analyzed_id", flat=True
        )
        check = CheckContentsAnalyzed.objects.filter(id__in=check_ids, is_done=False).first()
        if not self._all_content_analyzed(check):
            return
        self._callback_task_by_application[check.application_name](check.callback_id)
        check.is_done = True
        check.save()

    @staticmethod
    def _all_content_analyzed(check: CheckContentsAnalyzed) -> bool:
        if not check:
            return False
        all_content_ids = check.checkcontentsanalyzeddetail_set.filter().values_list("learn_content_id", flat=True)
        any_content_was_analyzed = not LearnContent.objects.filter(id__in=all_content_ids, analyzed=False).exists()
        return any_content_was_analyzed
