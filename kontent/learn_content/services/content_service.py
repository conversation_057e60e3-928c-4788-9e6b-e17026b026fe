# -*- coding: utf-8 -*-
import re
from typing import List, Sequence, Optional
from urllib.parse import urlparse
from uuid import uuid4
from django.core.files.storage import default_storage
from django.forms import model_to_dict
from elasticsearch import NotFoundError

from config import settings
from config.celery import app
from config.settings import ELASTIC<PERSON><PERSON>CH_INDEX
from constants import TOTAL_SECONDS_IN_A_MINUTE
from custom.keeps_exception_handler import KeepsBadRequestError, KeepsInvalidFileUrl
from learn_content.models import LearnContent, ContentType, LearnContentDocument
from analyze import (<PERSON><PERSON>zer,
                     AnalyzeVideoAudio,
                     AnalyzeMsOffice,
                     AnalyzePDF,
                     AnalyzeImage,
                     YoutubeVideoAnalyzer,
                     AnalyzeVimeo,
                     AnalyzeSoundcloud,
                     AnalyzeGdrive,
                     AnalyzeHtmlFiles, AnalyzeHtmlLink)
from task_names import ANALYZE_CONTENT, COMPRESS_CONTENT
from utils import format_file_name

SOUND_CLOUD_REGEX = r"^(https?:\/\/)?(www.)?(m\.)?soundcloud\.com\/[\w\-\.]+(\/)+[\w\-\.]+/?"


# pylint: disable=empty-docstring
# pylint: disable=bad-builtin
# pylint: disable=no-value-for-parameter
class ContentService:

    def __init__(self, elasticsearch, default_content_duration: int = 10, default_content_point: int = 10):
        self._files_allowed = ' '.join([c.extensions for c in ContentType.objects.all()])
        self.s3_url = '{}/{}'.format(settings.AWS_BASE_S3_URL, settings.AWS_BUCKET_NAME)
        self.connection = elasticsearch
        self.default_content_duration = default_content_duration
        self.default_content_point = default_content_point

    @staticmethod
    def copy(learn_content_id: str):
        learn_content = LearnContent.objects.get(id=learn_content_id)
        try:
            learn_content_document = LearnContentDocument().get(learn_content_id)
        except NotFoundError:
            learn_content_document = None
        copy = LearnContent(**{**model_to_dict(learn_content), "content_type": learn_content.content_type})
        copy.id = uuid4()
        copy.save()
        if learn_content_document:
            LearnContentDocument(**learn_content_document.to_dict(), meta={'id': copy.id}).save()
        return copy

    # pylint: disable=consider-iterating-dictionary
    def create_and_analyze(self, data: dict, workspace_id: str):
        """
        """
        if not data.get("name"):
            raise KeepsBadRequestError(i18n="content_name_required",
                                       detail="Content name is required. Set filed 'name'")

        content_flow = {
            "file": self.file_flow,
            "link": self.link_flow,
            "html_file": self.html_file_flow,
            "html": self.html_flow,
        }

        flow = list(filter(lambda x: x in list(content_flow.keys()), data))
        if not flow:
            raise KeepsBadRequestError(i18n="content_type_key",
                                       detail="Content type key not allowed. Expected: file, html, html_file or scorm")
        flow = flow[0]

        result, content_type = content_flow[flow](data, workspace_id)

        content = self.save_content_model(content_type, data, result)
        self.create_content_doc(content, result)

        if not content.analyzed:
            app.send_task(ANALYZE_CONTENT, args=(content.id,))
        if not content.compressed and content.is_whatsapp_content:
            app.send_task(COMPRESS_CONTENT, args=(content.id,))
        return content

    def save_content_model(self, content_type, data, result) -> LearnContent:
        content = LearnContent()
        if data.get("id"):
            content.id = data["id"]
        content.name = data.get("name")
        content.url = result.get("url")
        content.content_type = content_type
        content.transcribe_job = result.get("transcribe_job")
        content.analyzed = result.get("analyzed", False)
        content.duration = result.get("duration")
        content.file_size = result.get("file_size")
        content.original_file_size = result.get("original_file_size")
        content.file_mime_type = result.get("file_mime_type")
        content.is_whatsapp_content = data.get("is_whatsapp_content", False)
        content.save()
        return content

    @staticmethod
    def create_content_doc(instance, process_data):
        learn_document = LearnContentDocument(
            meta={'id': instance.id},
            name=instance.name,
            description=instance.description,
            category=instance.category.id if instance.category else None,
            content_type={
                "id": instance.content_type.id if instance.content_type else None,
                "name": instance.content_type.name if instance.content_type else None,
                "image": instance.content_type.image if instance.content_type else None,
                "image_cover": instance.content_type.image_cover if instance.content_type else None,
            },
            url=instance.url,
            analyzed=instance.analyzed,
            content_transcript=process_data.get('transcript', None),
            duration=process_data.get("duration", None),
            points=process_data.get("points", None),
            tags=process_data.get("tags", None),
            summary=process_data.get("summary", None),
            entities=process_data.get("entities", None),
            language=process_data.get("language", None),
            is_whatsapp_content=process_data.get("is_whatsapp_content"),
            is_valid_whatsapp_content=process_data.get("is_valid_whatsapp_content"),
            file_size=process_data.get("file_size"),
            file_mime_type=process_data.get("file_mime_type"),
            original_file_size=process_data.get("original_file_size"),
            compressed=process_data.get("compressed", False),
            created_date=instance.created_date,
            updated_date=instance.updated_date
        )
        learn_document.save()

    def file_flow(self, data: dict, workspace_id: str):
        file_in_memory = data.get('file')
        file_name = data.get('name')
        content_source, new_file_name, file_extension = self.create_temp_file(file_in_memory, file_name)
        content_type = ContentType.objects.filter(extensions__icontains=file_extension).first()
        points_rules = content_type.contentpointrule_set.first()
        analyzer = Analyzer()

        service_map = {
            "audio": {
                "command": AnalyzeVideoAudio, "ext": ['mp3', 'mp4', 'webm']},
            "ms_office": {
                "command": AnalyzeMsOffice, "ext": ['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx']},
            "pdf": {
                "command": AnalyzePDF, "ext": ['pdf', ]},
            "image": {
                "command": AnalyzeImage, "ext": ['png', 'jpeg', 'jpg', 'gif', 'webp']},
        }
        command = [service_map[x] for x in service_map if file_extension in service_map[x]['ext']][0]['command']

        analyzer.set_analyze_command(command(file_path=content_source,
                                             file_name=f'{new_file_name}.{file_extension}',
                                             points_rules=points_rules.points,
                                             points_quantity=points_rules.quantity,
                                             workspace_id=workspace_id))
        result = analyzer.process_content()

        return result, content_type

    @staticmethod
    def link_flow(data: dict, workspace_id: str):
        service_map = [
            {
                "command": YoutubeVideoAnalyzer, "content_type": "youtube",
                "regex": r"^(http(s)??\:\/\/)?(www\.)?((youtube\.com\/watch\?v=)|(youtu.be\/))([^&=%\?]{11})"
            },
            {
                "command": AnalyzeVimeo, "content_type": "vimeo",
                "regex": r"^(http|https)?:\/\/(www\.)?vimeo.com\/(?:channels\/(?:\w+\/)?|groups\/([^\/]*)\/videos\/|)(\d+)(?:|\/\?)"
            },
            {
                "command": AnalyzeSoundcloud, "content_type": "soundcloud",
                "regex": SOUND_CLOUD_REGEX
            },
            {
                "command": AnalyzeGdrive, "content_type": "gdrive",
                "regex": r"^https\:\/\/docs\.google\.com\/(document|presentation|spreadsheets)"
            },
        ]
        link = data.get('link').strip()
        service = [x for x in service_map if re.match(x['regex'], link)]
        if not service:
            raise KeepsInvalidFileUrl()
        service = service[0]

        if service["content_type"] == 'gdrive':
            gdrive_type = str(urlparse(link).path.split('/')[1])
            extensions = {'presentation': 'pptx', 'spreadsheets': 'xlsx', 'document': 'docx'}
            extension = extensions[gdrive_type]
        else:
            extension = service["content_type"]

        analyzer = Analyzer()
        content_type = ContentType.objects.filter(extensions__icontains=extension).first()
        points_rules = content_type.contentpointrule_set.first()
        analyzer.set_analyze_command(service["command"](url=link,
                                                        points_rules=points_rules.points,
                                                        points_quantity=points_rules.quantity))
        result = analyzer.process_content()
        return result, content_type

    def html_file_flow(self, data: dict, workspace_id: str):
        analyzer = Analyzer()
        file = data.get('html_file')
        index_file = data.get('index_file') or None
        content_source, file_name, _ = self.create_temp_file(file)
        content_type = ContentType.objects.filter(name='HTML').first()
        points_rules = content_type.contentpointrule_set.first()

        analyzer.set_analyze_command(AnalyzeHtmlFiles(
            file_path=content_source,
            file_name=file_name,
            index_file=index_file,
            duration=int(data.get('time_minutes', 1)) * TOTAL_SECONDS_IN_A_MINUTE,
            points_rules=points_rules.points,
            points_quantity=points_rules.quantity)
        )
        result = analyzer.process_content()
        return result, content_type

    @staticmethod
    def html_flow(data: dict, workspace_id: str):
        analyzer = Analyzer()
        content_type = ContentType.objects.filter(name='HTML').first()
        extensions = content_type.extensions.split()
        url_parsed = urlparse(data.get('html'))

        if url_parsed.netloc not in extensions:
            raise KeepsBadRequestError(i18n="invalid_link_type", detail='Invalid html link')

        points_rules = content_type.contentpointrule_set.first()
        analyzer.set_analyze_command(AnalyzeHtmlLink(
            url=data.get('html'),
            duration=int(data.get('time_minutes', 1)) * TOTAL_SECONDS_IN_A_MINUTE,
            points_rules=points_rules.points,
            points_quantity=points_rules.quantity)
        )
        result = analyzer.process_content()
        return result, content_type

    def create_temp_file(self, file, filename=None):
        extension = file.name.split('.')[-1].lower()
        if extension not in self._files_allowed.split(' '):
            raise KeepsBadRequestError(
                i18n="invalid_file_type",
                detail=f'Content type not allowed. Upload files or send links as: {self._files_allowed}'
            )

        if not filename:
            filename = str(uuid4())

        unique_suffix = uuid4().hex[:4]
        filename = f"{format_file_name(filename)}_{unique_suffix}"
        full_filename = "{}.{}".format(filename, extension)

        self._save_file(file, full_filename)

        return '{}/{}'.format(settings.TEMP_UPLOAD_FOLDER, full_filename), filename, extension

    @staticmethod
    def _save_file(file, filename):
        with open(default_storage.path(filename), 'wb+') as destination:
            for chunk in file.chunks():
                destination.write(chunk)
            destination.close()

    def list_documents_by(self, ids: Sequence[str]) -> Sequence[dict]:
        query = {"bool": {"filter": {"ids": {"values": ids}}}}
        main_hits = self.connection.search(index=ELASTICSEARCH_INDEX, body={"query": query, "size": len(ids)})["hits"]
        return main_hits["hits"] if main_hits else []

    def _format_content_document(self, document: dict) -> dict:
        body = document["_source"]
        body["id"] = document["_id"]
        body["url"] = re.sub(self.s3_url, settings.AWS_STREAMING_URL, body["url"])
        if not body.get("content_type"):
            return body
        if body.get("analyzed"):
            body["duration"] = body.get("duration") or self.default_content_duration
            body["points"] = body.get("points") or self.default_content_point
        content_type = ContentType.objects.filter(id=body["content_type"]["id"]).first()
        if content_type:
            body["content_type"]["image"] = content_type.image
            body["content_type"]["image_cover"] = content_type.image_cover

        return body

    def _learn_content_to_dict(self, content: LearnContent):
        return {
            'name': content.name,
            'content_type': {
                'id': str(content.content_type.id),
                'name': content.content_type.name,
                'image': content.content_type.image,
                'image_cover': content.content_type.image_cover
            },
            'url': re.sub(self.s3_url, settings.AWS_STREAMING_URL, content.url),
            'analyzed': content.analyzed,
            'created_date': str(content.created_date),
            'updated_date': str(content.updated_date),
            'content_transcript': "",
            'entities': [],
            'language': 'pt',
            'tag': [],
            'summary': "",
            'duration': self.default_content_duration if content.analyzed else 0,
            'points': self.default_content_point if content.analyzed else 0
        }

    def get_content_documents(self, ids: List[str]):
        formatted_documents = []
        documents = self.list_documents_by(ids)
        found_document_ids = [document["_id"] for document in documents]
        not_found_document_ids = [_id for _id in ids if _id not in found_document_ids]
        local_contents = list(LearnContent.objects.filter(id__in=not_found_document_ids))

        for document in documents:
            formatted_documents.append(self._format_content_document(document))
        for content in local_contents:
            formatted_documents.append(self._learn_content_to_dict(content))

        return formatted_documents

    def get_document(self, content_uuid) -> dict:
        try:
            instance = LearnContentDocument().get(content_uuid)
            new_url = re.sub(self.s3_url, settings.AWS_STREAMING_URL, instance.url)
            instance.url = new_url
            if instance.analyzed:
                instance.duration = instance.duration or self.default_content_duration
                instance.points = instance.points or self.default_content_point
            return instance.to_dict()
        except Exception:
            data = self.get_db_object(content_uuid)
            return data

    @staticmethod
    def compute_points(duration: int, quantity: int, points_by_quantity: int) -> int:
        return int(duration / quantity * points_by_quantity)

    def update(self, content_id: str, data: dict):
        duration = data.get("duration")
        if not duration:
            return None
        document = LearnContentDocument().get(content_id)
        type_name = document.content_type.name
        document.duration = duration
        content_type = ContentType.objects.get(name=type_name)
        point_rules = content_type.contentpointrule_set.first()
        document.points = self.compute_points(document.duration, point_rules.quantity, point_rules.points)
        document.save()

    def get_db_object(self, content_uuid) -> Optional[dict]:
        """
        If fail to get document into Elasticsearch, get basic data into database.

        :param content_uuid:
        :return data dict:
        """
        instance = LearnContent.objects.filter(id=content_uuid).first()
        if not instance:
            return None
        data = {
            'name': instance.name,
            'content_type': {
                'id': str(instance.content_type.id),
                'name': instance.content_type.name,
                'image': instance.content_type.image,
                'image_cover': instance.content_type.image_cover
            },
            'url': re.sub(self.s3_url, settings.AWS_STREAMING_URL, instance.url),
            'analyzed': True,
            'created_date': str(instance.created_date),
            'updated_date': str(instance.updated_date),
            'content_transcript': "",
            'entities': [],
            'language': 'pt',
            'tag': [],
            'summary': "",
            'duration': 0,
            'points': 0
        }
        return data
