import os
from uuid import uuid4

from config import settings
from analyze import Analy<PERSON>, AnalyzeScorm
from config.settings import TEMP_UPLOAD_FOLDER
from learn_content.models import LearnContent, ContentPointRule
from learn_content.models.content_type import ContentType
from learn_content.models.documents import LearnContentDocument


class ScormService:

    # pylint: disable=too-many-locals
    # pylint: disable=no-value-for-parameter
    def process_scorm(self, data, serializer):
        analyzer = Analyzer()
        file = data.get('scorm_package')
        content_source, file_name, _ = self.create_temp_file(file)
        content_type = ContentType.objects.filter(name='SCORM').first()
        point_rule: ContentPointRule = content_type.contentpointrule_set.first()
        duration = int(data.get("duration"))

        if duration == 0:
            duration = 10

        analyzer.set_analyze_command(
            AnalyzeScorm(
                file_path=content_source,
                file_name=file_name,
                duration=duration,
                points_rules=point_rule.points,
                points_quantity=point_rule.quantity
            )
        )
        result = analyzer.process_content()
        course_steps = result.get('course_steps')

        for i, step in enumerate(course_steps):
            _contents = []
            for j, content in enumerate(step['contents']):
                learn_content = LearnContent()
                learn_content.name = content.get("title")
                learn_content.url = content.get('url')
                learn_content.description = content.get('description')
                learn_content.content_type = content_type
                learn_content.analyzed = True
                learn_content.save()
                learn_content.order = j + 1
                _contents.append(learn_content)

                # each 2 minutes 1 point
                self.create_content_doc(learn_content, content)

            course_steps[i]['contents'] = serializer(instance=_contents, many=True).data

        result['course_steps'] = course_steps

        if os.path.exists(content_source):
            os.remove(content_source)

        return result

    @staticmethod
    def create_content_doc(instance, process_data):
        learn_document = LearnContentDocument(
            meta={'id': instance.id},
            name=instance.name,
            description=instance.description,
            category=instance.category.id if instance.category else None,
            content_type={
                "id": instance.content_type.id if instance.content_type else None,
                "name": instance.content_type.name if instance.content_type else None,
                "image": instance.content_type.image if instance.content_type else None,
                "image_cover": instance.content_type.image_cover if instance.content_type else None,
            },
            url=instance.url,
            analyzed=instance.analyzed,
            content_transcript=process_data.get('transcript', None),
            duration=process_data.get("duration", None),
            points=process_data.get("points", None),
            tags=process_data.get("tags", None),
            summary=process_data.get("summary", None),
            entities=process_data.get("entities", None),
            language=process_data.get("language", None),
            created_date=instance.created_date,
            updated_date=instance.updated_date)
        learn_document.save()
        return learn_document

    @staticmethod
    def create_temp_file(file):
        extension = file.name.split('.')[-1]
        new_file_name = str(uuid4())
        filename = "{}.{}".format(new_file_name, extension)

        with open(os.path.join(TEMP_UPLOAD_FOLDER, filename), 'wb+') as destination:
            for chunk in file.chunks():
                destination.write(chunk)

        return '{}/{}'.format(settings.TEMP_UPLOAD_FOLDER, filename), new_file_name, extension
