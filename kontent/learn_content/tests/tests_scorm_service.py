# -*- coding: utf-8 -*-
import uuid

import mock
from model_mommy import mommy
from django.test import TestCase

from learn_content.models import LearnContentDocument
from learn_content.models.learn_content import LearnContent, ContentType
from learn_content.services.scrom_service import ScormService


@mock.patch('learn_content.models.documents.learn_content_doc.LearnContentDocument.save', return_value=True)
class ScormServiceTestCase(TestCase):

    def setUp(self):
        self._scorm_service = ScormService()

    def test_create_content_doc(self, mock):
        content_type = mommy.make(ContentType, id=uuid.uuid4())
        instance = mommy.make(LearnContent, id=uuid.uuid4(),
                              name="Test",
                              description="Description test",
                              url="url.com",
                              analyzed=True,
                              content_type=content_type)

        data = {
                    "transcript": "transcript test",
                    "tags": [{"tag": "test", "relevance": 1}],
                    "entities": [],
                    "language": "pt",
                    "summary": "summarize",
                    "analyzed": True
                }
        result = self._scorm_service.create_content_doc(instance=instance, process_data=data)

        self.assertEqual(isinstance(result, LearnContentDocument), True)
        self.assertEqual(result.analyzed, True)
        self.assertEqual(result.category, None)
        self.assertEqual(result.content_transcript, "transcript test")
        self.assertEqual(result.content_type.name, content_type.name)
        self.assertEqual(result.content_type.id, content_type.id)
        self.assertEqual(result.description, "Description test")
        self.assertEqual(result.language, "pt")
        self.assertEqual(result.summary, "summarize")
        self.assertEqual(result.url, "url.com")
        self.assertEqual(result.tags, [{'tag': 'test', 'relevance': 1}])
