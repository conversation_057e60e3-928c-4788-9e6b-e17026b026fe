import os
import mock
from django.urls import reverse
from django.test import TestCase
from model_mommy import mommy

from config.settings import BASE_DIR
from .fixtures import scorm_data
from rest_framework.test import APIClient

from ..models import ContentType, ContentPointRule


@mock.patch('authentication.keeps_authentication.KeepsAuthentication._get_token_info', return_value={})
@mock.patch('analyze.scorm.AnalyzeScorm.execute', return_value=scorm_data)
@mock.patch('learn_content.models.documents.learn_content_doc.LearnContentDocument.save', return_value=True)
class MissionCategoryViewsetTestCase(TestCase):

    def setUp(self):
        self.client = APIClient()
        self.url = reverse('learn-content-scorm')
        self.content_type = mommy.make(ContentType, name='SCORM')
        self.content_type_rule = mommy.make(ContentPointRule, content_type=self.content_type, points=5, quantity=1)

    def test_create_scorm(self, mock_es_save, mock_analyze, mock_auth):
        scorm_file = f'{BASE_DIR}/learn_content/tests/files/sample_scorm.zip'
        response = self.client.post(self.url, data={
            "scorm_package": open(scorm_file, "rb"),
            "duration": 60
        })
        data = response.data
        print(data)
        self.assertEqual(data.get("course_title"), 'SCORM UNIT TEST')
        self.assertEqual(len(data.get("steps")), 2)
        self.assertEqual(data.get("steps")[0]['step_title'], 'STEP 1')
        self.assertEqual(data.get("steps")[0]['step_order'], 1)
        self.assertEqual(len(data.get("steps")[0]['contents']), 2)
        self.assertEqual(data.get("steps")[0]['contents'][0]['order'], 1)
        self.assertEqual(data.get("steps")[0]['contents'][0]['name'], 'content 1')
        self.assertEqual(data.get("steps")[0]['contents'][0]['description'], 'desc content 1')
        self.assertEqual(data.get("steps")[0]['contents'][0]['url'], 'url.com.1')
        self.assertEqual(data.get("steps")[0]['contents'][0]['analyzed'], True)
        self.assertEqual(data.get("steps")[0]['contents'][1]['order'], 2)
        self.assertEqual(data.get("steps")[0]['contents'][1]['name'], 'content 2')
        self.assertEqual(data.get("steps")[0]['contents'][1]['description'], 'desc content 2')
        self.assertEqual(data.get("steps")[0]['contents'][1]['url'], 'url.com.2')
        self.assertEqual(data.get("steps")[0]['contents'][1]['analyzed'], True)
        self.assertEqual(data.get("steps")[0]['step_title'], 'STEP 1')
        self.assertEqual(data.get("steps")[0]['step_order'], 1)

        self.assertEqual(len(data.get("steps")[1]['contents']), 2)
        self.assertEqual(data.get("steps")[1]['contents'][0]['order'], 1)
        self.assertEqual(data.get("steps")[1]['contents'][0]['name'], 'content 1')
        self.assertEqual(data.get("steps")[1]['contents'][0]['description'], 'desc content 1')
        self.assertEqual(data.get("steps")[1]['contents'][0]['url'], 'url.com.1')
        self.assertEqual(data.get("steps")[1]['contents'][0]['analyzed'], True)
        self.assertEqual(data.get("steps")[1]['contents'][1]['order'], 2)
        self.assertEqual(data.get("steps")[1]['contents'][1]['name'], 'content 2')
        self.assertEqual(data.get("steps")[1]['contents'][1]['description'], 'desc content 2')
        self.assertEqual(data.get("steps")[1]['contents'][1]['url'], 'url.com.2')
        self.assertEqual(data.get("steps")[1]['contents'][1]['analyzed'], True)