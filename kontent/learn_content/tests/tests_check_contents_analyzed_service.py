import uuid
from unittest.mock import Magic<PERSON>ock

import mock
from django.test import TestCase
from model_mommy import mommy

from constants import KONQUEST_APPLICATION
from learn_content.models import LearnContent
from learn_content.models.check_contents_analyzed import CheckContentsAnalyzed
from learn_content.models.check_contents_analyzed_detail import CheckContentsAnalyzedDetail
from learn_content.services.check_contents_analyzed_service import CheckContentsAnalyzedService



class TestsCheckContentsAnalyzedService(TestCase):
    def setUp(self) -> None:
        self.callback_function = MagicMock()
        self.learn_content_1 = mommy.make(LearnContent)
        self.learn_content_2 = mommy.make(LearnContent)
        self.service = CheckContentsAnalyzedService({KONQUEST_APPLICATION: self.callback_function})

    def test_create(self):
        callback_id = uuid.uuid4()
        learn_contents_total = 2
        check_instance = self.service.create({
            "learn_content_ids": [self.learn_content_1.id, self.learn_content_2.id],
            "application_name": KONQUEST_APPLICATION,
            "callback_id": callback_id
        })

        self.assertEqual(check_instance.callback_id, callback_id)
        self.assertEqual(check_instance.application_name, KONQUEST_APPLICATION)
        self.assertEqual(check_instance.checkcontentsanalyzeddetail_set.count(), learn_contents_total)

    def test_when_all_content_analyzed_call_callback(self):
        check = self._create_check()
        self.learn_content_1.analyzed = True
        self.learn_content_2.analyzed = True
        LearnContent.objects.bulk_update([self.learn_content_1, self.learn_content_2], fields=["analyzed"])

        self.service.make_check(self.learn_content_1)

        self.callback_function.assert_called_with(str(check.callback_id))

    def test_do_nothing_when_any_content_was_not_analyzed(self):
        self._create_check()
        self.learn_content_1.analyzed = True
        self.learn_content_2.analyzed = False
        LearnContent.objects.bulk_update([self.learn_content_1, self.learn_content_2], fields=["analyzed"])

        self.service.make_check(self.learn_content_1)

        self.callback_function.assert_not_called()

    def _create_check(self):
        check = mommy.make(CheckContentsAnalyzed, application_name=KONQUEST_APPLICATION, callback_id=uuid.uuid4())
        mommy.make(CheckContentsAnalyzedDetail, learn_content=self.learn_content_1, check_contents_analyzed=check)
        mommy.make(CheckContentsAnalyzedDetail, learn_content=self.learn_content_2, check_contents_analyzed=check)
        return check
