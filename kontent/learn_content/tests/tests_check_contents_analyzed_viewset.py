import uuid

from django.test import TestCase
from mock import mock
from model_mommy import mommy
from rest_framework.reverse import reverse
from rest_framework.status import HTTP_204_NO_CONTENT, HTTP_400_BAD_REQUEST
from rest_framework.test import APIClient

from constants import KONQUEST_APPLICATION
from learn_content.models import LearnContent


@mock.patch('authentication.keeps_authentication.KeepsAuthentication._get_token_info', return_value={})
class TestsCheckContentsAnalyzedViewset(TestCase):
    def setUp(self) -> None:
        self.client = APIClient()

        self.url = reverse("check-contents-analyzed")
        self.headers = {"HTTP_X_CLIENT": str(uuid.uuid4())}

    def test_create(self, get_token_info: mock.MagicMock):
        learn_content = mommy.make(LearnContent)
        payload = {
            "learn_content_ids": [learn_content.id,],
            "application_name": KONQUEST_APPLICATION,
            "callback_id": uuid.uuid4()
        }

        response = self.client.post(self.url, data=payload, **self.headers, format="json")

        self.assertEqual(response.status_code, HTTP_204_NO_CONTENT)
