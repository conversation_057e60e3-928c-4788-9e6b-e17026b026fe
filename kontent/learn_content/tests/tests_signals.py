from django.test import TestCase
from mock import mock
from model_mommy import mommy

from learn_content.models import LearnContent


class TestsSignals(TestCase):
    @mock.patch("learn_content.tasks.check_contents_analyzed_task.delay")
    def test_create_new_mission_evaluation_notification_receiver(self, check_contents_analyzed_task: mock.MagicMock):
        content = mommy.make(LearnContent)
        content.analyzed = True
        content.save()

        check_contents_analyzed_task.assert_called_with(content.id)
