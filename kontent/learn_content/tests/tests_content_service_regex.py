from django.test import TestCase
import re

from learn_content.services.content_service import SOUND_CLOUD_REGEX


class TestContentServiceRegex(TestCase):
    def test_should_sound_cloud_regex_match_with_link_with_params(self):
        self.assertIsNotNone(re.match(
            SOUND_CLOUD_REGEX,
            'https://soundcloud.com/stryvmusic/ed-sheeran-thinking-out-loud-stryv-remix-1?'
            'in=ilyana<PERSON>man/sets/bestofchillnation&utm_source=clipboard&utm_medium=text&utm_campaign=social_sharing'
        ))

    def test_should_sound_cloud_regex_match_with_link_without_params(self):
        self.assertIsNotNone(re.match(
            SOUND_CLOUD_REGEX,
            'https://soundcloud.com/stryvmusic/ed-sheeran-thinking-out-loud-stryv-remix-1'
        ))
