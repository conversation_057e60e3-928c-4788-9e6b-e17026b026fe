import uuid
from django.test import TestCase
from model_mommy import mommy
from learn_content.models import LearnContent
from learn_content.models.content_type import ContentType
from file_types import PDF, MEDIA_LINK, GOOGLE_DRIVE, MSOFFICE_FILE, IMAGE, SCORM

class TestsLearnContent(TestCase):
    def test_pdf_file_type(self):
        pdf_content_type, _ = ContentType.objects.get_or_create(name=PDF)
        pdf_content = mommy.make(
            LearnContent,
            content_type=pdf_content_type,
            name="Test PDF Content",
            url="https://example.com"
        )
        self.assertTrue(pdf_content.is_pdf[0])
        self.assertEqual(pdf_content.file_type, PDF)

    def test_media_file_type(self):
        media_content = mommy.make(
            LearnContent,
            transcribe_job="12345",
            name="Test Media Content",
            url="https://example.com", 
        )
        self.assertTrue(media_content.is_media_file[0])

    def test_media_link_type(self):
        media_link_content_type, _ = ContentType.objects.get_or_create(name='Video')
        media_link_content = mommy.make(
            LearnContent,
            content_type=media_link_content_type,
            name="Test Media Link Content",
            url="https://example.com"
        )
        self.assertTrue(media_link_content.is_media_link[0])
        self.assertEqual(media_link_content.file_type, MEDIA_LINK)

    def test_drive_link_type(self):
        drive_link_content_type, _ = ContentType.objects.get_or_create(name=GOOGLE_DRIVE)
        drive_link_content = mommy.make(
            LearnContent,
            name="Test Drive Link Content",
            url="https://docs.google.com/document/d/12345",
            content_type=drive_link_content_type
        )
        self.assertTrue(drive_link_content.is_drive_link[0])
        self.assertEqual(drive_link_content.file_type, GOOGLE_DRIVE)

    def test_ms_office_file_type(self):
        ms_office_content_type, _ = ContentType.objects.get_or_create(name='Spreadsheet')
        ms_office_content = mommy.make(
            LearnContent,
            content_type=ms_office_content_type,
            name="Test MS Office Content",
            url="https://example.com"
        )
        self.assertTrue(ms_office_content.is_ms_office_file[0])
        self.assertEqual(ms_office_content.file_type, MSOFFICE_FILE)

    def test_scorm_file_type(self):
        scorm_content_type, _ = ContentType.objects.get_or_create(name=SCORM)
        scorm_content = mommy.make(
            LearnContent,
            content_type=scorm_content_type,
            name="Test SCORM Content",
            url="https://example.com"
        )
        self.assertTrue(scorm_content.is_scorm_file[0])
        self.assertEqual(scorm_content.file_type, SCORM)

    def test_image_file_type(self):
        image_content_type, _ = ContentType.objects.get_or_create(name='Image')
        image_content = mommy.make(
            LearnContent,
            content_type=image_content_type,
            name="Test Image Content",
            url="https://example.com"
        )
        self.assertTrue(image_content.is_image[0])
        self.assertEqual(image_content.file_type, IMAGE)
