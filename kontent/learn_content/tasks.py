from celery import shared_task

from config.settings import CELERY_QUEUE
from learn_content.services.check_contents_analyzed_service import CheckContentsAnalyzedService
from utils.task_transaction import task_transaction


@shared_task(queue=CELERY_QUEUE, ignore_result=True)
def check_contents_analyzed_task(content_analyzed_id: str):
    with task_transaction(check_contents_analyzed_task.__name__) as container:
        service: CheckContentsAnalyzedService = container.check_contents_analyzed_service()
        service.make_check(content_analyzed_id)
