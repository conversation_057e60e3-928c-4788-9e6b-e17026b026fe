from django.db.models.signals import post_save

from learn_content.models import Learn<PERSON>ontent
from learn_content.tasks import check_contents_analyzed_task
from utils.kp_receiver import kp_receiver


@kp_receiver(post_save, sender=LearnContent)
def learn_content_analyzed(sender, instance: <PERSON>rn<PERSON>ontent, created, **kwargs):
    if created:
        return
    if instance.analyzed:
        check_contents_analyzed_task.delay(instance.id)
