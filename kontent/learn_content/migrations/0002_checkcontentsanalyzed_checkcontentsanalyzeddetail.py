# Generated by Django 2.2 on 2023-07-12 11:48

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('learn_content', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='CheckContentsAnalyzed',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('application_name', models.CharField(max_length=200, verbose_name='Application name')),
                ('callback_id', models.CharField(max_length=200, verbose_name='Callback id')),
                ('is_done', models.BooleanField(default=False, verbose_name='Is done?')),
            ],
            options={
                'db_table': 'check_contents_analyzed',
            },
        ),
        migrations.CreateModel(
            name='CheckContentsAnalyzedDetail',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('check_contents_analyzed', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='learn_content.CheckContentsAnalyzed', verbose_name='Check contents analyzed')),
                ('learn_content', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='learn_content.LearnContent', verbose_name='Learn content')),
            ],
            options={
                'db_table': 'check_content_analyzed_detail',
            },
        ),
    ]
