# Generated by Django 2.2 on 2024-12-05 05:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('learn_content', '0003_auto_20230713_1515'),
    ]

    operations = [
        migrations.AddField(
            model_name='learncontent',
            name='is_whatsapp_content',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='learncontent',
            name='is_valid_whatsapp_content',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='learncontent',
            name='duration',
            field=models.IntegerField(blank=True, null=True, verbose_name='File Duration'),
        ),
        migrations.AddField(
            model_name='learncontent',
            name='file_size',
            field=models.FloatField(blank=True, null=True, verbose_name='File Size (MB)'),
        ),
        migrations.AddField(
            model_name='learncontent',
            name='original_file_size',
            field=models.FloatField(blank=True, null=True, verbose_name='Original File Size (MB)'),
        ),
        migrations.AddField(
            model_name='learncontent',
            name='compressed',
            field=models.<PERSON>oleanField(default=False, verbose_name='Compressed'),
        ),
        migrations.AddField(
            model_name='learncontent',
            name='file_mime_type',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
    ]
