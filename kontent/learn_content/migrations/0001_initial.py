# Generated by Django 2.2 on 2022-04-15 22:25

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ContentCategory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('image', models.URLField(verbose_name='Pulse Type Image')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='Updated Date')),
            ],
            options={
                'verbose_name_plural': 'Content Categories',
                'db_table': 'content_category',
            },
        ),
        migrations.CreateModel(
            name='ContentType',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('image', models.URLField(blank=True, null=True, verbose_name='Content Type Image')),
                ('image_cover', models.URLField(blank=True, null=True, verbose_name='Content Type Image Cover')),
                ('extensions', models.CharField(blank=True, max_length=500, null=True, verbose_name='Extensions')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='Updated Date')),
            ],
            options={
                'verbose_name_plural': 'Content Types',
                'db_table': 'content_type',
            },
        ),
        migrations.CreateModel(
            name='LearnContent',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('url', models.URLField(max_length=500, verbose_name='URL')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='Updated Date')),
                ('analyzed', models.BooleanField(default=False, verbose_name='Analyzed')),
                ('transcribe_job', models.CharField(blank=True, max_length=200, null=True, verbose_name='Transcribe Job')),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='learn_content.ContentCategory', verbose_name='Category')),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='learn_content.ContentType', verbose_name='Type')),
            ],
            options={
                'verbose_name_plural': 'Learn Content',
                'db_table': 'learn_content',
            },
        ),
        migrations.CreateModel(
            name='ContentPointRule',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('points', models.IntegerField(verbose_name='Points')),
                ('unit', models.CharField(max_length=100, verbose_name='Unit')),
                ('quantity', models.IntegerField(verbose_name='Quantity')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='Updated Date')),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='learn_content.ContentType', verbose_name='Content Category')),
            ],
            options={
                'verbose_name_plural': 'Content Point Rules',
                'db_table': 'content_point_rule',
            },
        ),
    ]
