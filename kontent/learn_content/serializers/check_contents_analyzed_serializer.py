from rest_framework import serializers

from learn_content.models.application_name_enum import ApplicationNameEnum


class CheckContentsAnalyzedSerializer(serializers.Serializer):
    learn_content_ids = serializers.ListSerializer(
        child=serializers.CharField(), required=True
    )
    application_name = serializers.ChoiceField(ApplicationNameEnum.choices(), required=True)
    callback_id = serializers.CharField(required=True)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass
