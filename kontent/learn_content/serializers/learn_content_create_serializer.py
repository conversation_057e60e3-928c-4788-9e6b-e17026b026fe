import uuid

from rest_framework import serializers


class LearnContentCreateSerializer(serializers.Serializer):
    id = serializers.UUIDField(default=uuid.uuid4, read_only=True)
    name = serializers.CharField()
    file = serializers.FileField(required=False, allow_null=True)
    link = serializers.URLField(required=False, allow_null=True)
    description = serializers.CharField(required=False, allow_blank=True)
    is_whatsapp_content = serializers.BooleanField(default=False)

    def validate(self, data: dict):
        file = data.get("file")
        link = data.get("link")

        if not file and not link:
            raise serializers.ValidationError("Either 'file' or 'link' must be provided.")

        if file and link:
            raise serializers.ValidationError("Only one of 'file' or 'link' can be provided, not both.")

        return data
