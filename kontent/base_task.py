import celery
import django

from custom import DISCORD_WEBHOOK


class BaseTask(celery.Task):
    autoretry_for = (Exception,)
    max_retries = 5
    default_retry_delay = 30
    reject_on_worker_lost = True
    acks_on_failure_or_timeout = False
    acks_late = True

    def run(self, *args, **kwargs):
        super().run(*args, **kwargs)

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        django.db.connections.close_all()
        title = f'Celery Task. Task id: {task_id}'
        DISCORD_WEBHOOK.emit_short_message(title, exc)
