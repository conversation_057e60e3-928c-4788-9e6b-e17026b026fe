from string import Template

from sqlalchemy.engine import Connection, Engine, CursorResult

from config.settings import BASE_DIR


class QuerySelector:
    def __init__(self, db_engine: Engine, template_folder: str):
        self._engine = db_engine
        self._template_folder = template_folder

    def list(self, query_file_name: str):
        with self._engine.connect() as connection:
            result_proxy = self._load_query(connection, query_file_name)
            results = list(result_proxy)
        return results

    # pylint: disable=invalid-name
    # pylint: disable=consider-using-with
    def _load_query(self, connection: Connection, query_file_name: str) -> CursorResult:
        file_message = open(f"{BASE_DIR}/{self._template_folder}/{query_file_name}.txt")  # noqa: SIM115
        sql = Template(file_message.read()).substitute()
        rs = connection.execute(sql)
        return rs
