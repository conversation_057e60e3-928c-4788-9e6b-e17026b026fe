from elasticsearch import Elasticsearch

from config.settings import DAT<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>CH_HOST, <PERSON>LAST<PERSON><PERSON><PERSON>CH_USER, <PERSON>LA<PERSON><PERSON><PERSON>ARCH_PASS, C<PERSON><PERSON>ER_QUERIES_DIRECTORY
from cleaner.cleaner_service import CleanerService
from cleaner.database import create_engine_and_session
from cleaner.query_selector import QuerySelector


if __name__ == '__main__':
    DATABASE = DATABASES['default']
    DATABASE_URL = f"postgresql://{DATABASE['USER']}:{DATABASE['PASSWORD']}@{DATABASE['HOST']}:{DATABASE['PORT']}/{DATABASE['NAME']}"
    elasticsearch = Elasticsearch(ELASTICSEARCH_HOST, http_auth=(ELASTICSEARCH_USER, ELASTICSEARCH_PASS))
    engine, _ = create_engine_and_session(DATABASE_URL)
    query_selector = QuerySelector(engine, CLEANER_QUERIES_DIRECTORY)
    CleanerService(query_selector, elasticsearch).clean_elastic_search()
