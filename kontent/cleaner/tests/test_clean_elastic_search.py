import uuid

import pytest
from elasticmock import FakeElasticsearch
from elasticsearch_dsl import connections
from sqlalchemy.engine import Engine

from cleaner.cleaner_service import CleanerService
from cleaner.query_selector import QuerySelector
from config.settings import CLEANER_QUERIES_DIRECTORY, ELASTICSEARCH_INDEX

CONTENT_SAVED_IN_DATABASE_IDS = [str(uuid.uuid4()), str(uuid.uuid4())]


class QuerySelectorMocked(QuerySelector):
    def __init__(self, db_engine: Engine, template_folder: str):
        super().__init__(db_engine, template_folder)

    def list(self, query_file_name: str, **query_filters):
        return [{"id": CONTENT_SAVED_IN_DATABASE_IDS[0]}, {"id": CONTENT_SAVED_IN_DATABASE_IDS[1]}]


@pytest.fixture
def elasticsearch_connection():
    conn = FakeElasticsearch("localhost")
    connections.create_connection(hosts=['localhost'], timeout=20)
    return conn


@pytest.fixture
def engine():
    return None


def test_clean_elastic_search(elasticsearch_connection, engine, mocker):
    query_selector = QuerySelectorMocked(engine, CLEANER_QUERIES_DIRECTORY)
    delete_by_query = mocker.patch("elasticsearch.Elasticsearch.delete_by_query")

    CleanerService(query_selector, elasticsearch_connection).clean_elastic_search()

    delete_by_query.assert_called_with(
        index=ELASTICSEARCH_INDEX,
        body={"query": {"bool": {"must_not": {"ids": {"values": CONTENT_SAVED_IN_DATABASE_IDS}}}}}
    )
