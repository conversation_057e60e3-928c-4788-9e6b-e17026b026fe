from elasticsearch import Elasticsearch

from config.settings import ELASTICSEARCH_INDEX
from cleaner.query_selector import QuerySelector

LIST_LEARN_CONTENTS = "list_learn_contents"


class CleanerService:
    def __init__(self, query_selector: QuerySelector, elastic_search: Elasticsearch):
        self.query_selector = query_selector
        self.elastic_search = elastic_search

    def clean_elastic_search(self):
        contents = self.query_selector.list(LIST_LEARN_CONTENTS)
        content_ids = [str(content["id"]) for content in contents]
        query = {"bool": {"must_not": {"ids": {"values": content_ids}}}}
        self.elastic_search.delete_by_query(index=ELASTICSEARCH_INDEX, body={"query": query})
