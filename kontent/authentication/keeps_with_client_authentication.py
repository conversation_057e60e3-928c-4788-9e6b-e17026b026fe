from authentication.keeps_authentication import KeepsAuthentication
from rest_framework import status
from rest_framework.exceptions import APIException


class MissingClientHeaderException(APIException):
    """
    Custom exception for missing or invalid 'x-client' header.
    """

    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "'x-client' header is required and was not provided."
    default_code = "bad_request"


class KeepsWithClientAuthentication(KeepsAuthentication):
    @staticmethod
    def _get_profile(request):
        """
        Retrieves the 'x-client' header (tenant-id) from the request.

        :param request: The HTTP request object.
        :return: The value of the 'x-client' header.
        :raises MissingClientHeaderException: If the 'x-client' header is missing.
        """
        x_client = request.META.get("HTTP_X_CLIENT")
        if not x_client:
            raise MissingClientHeaderException()
        return x_client
