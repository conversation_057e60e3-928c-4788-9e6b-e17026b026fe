[supervisord]
Description=Celery Service
nodaemon=true
loglevel=info
user=root

[program:celery]
directory=/app
command=celery -A tasks worker -P threads --loglevel=INFO -n worker1@%%h --concurrency=2
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
redirect_stderr=true
autostart=true
autorestart=true
startsecs=10
stopwaitsecs=600
killasgroup=true
priority=999

[program:scheduler]
directory=/app
command=python cron_schedulers.py
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
redirect_stderr=true
autostart=true
autorestart=true
startsecs=10
stopwaitsecs=600
priority=1000
