from __future__ import absolute_import
import os
from celery import Celery
from django.conf import settings

# set the default Django settings module for the 'celery' program.
from config.settings import CELERY_QUEUE, CELERY_IGNORE_RESULT, KONQUEST_CELERY_QUEUE

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
app = Celery('kontent')

# Using a string here means the worker will not have to
# pickle the object when using Windows.
app.config_from_object('django.conf:settings', namespace='CELERY_NAMESPACE')
app.autodiscover_tasks(lambda: settings.INSTALLED_APPS)
app.autodiscover_tasks(("tasks.content_analyzers.analyze_content", "tasks.compressors.compress_media_file"))
app.conf.task_default_queue = CELERY_QUEUE
app.conf.task_ignore_result = CELERY_IGNORE_RESULT

konquest_app = Celery('konquest')
konquest_app.config_from_object('django.conf:settings', namespace='CELERY_NAMESPACE')
konquest_app.conf.task_default_queue = KONQUEST_CELERY_QUEUE


@app.task(bind=True)
def debug_task(self):
    print('Request: {0!r}'.format(self.request))
