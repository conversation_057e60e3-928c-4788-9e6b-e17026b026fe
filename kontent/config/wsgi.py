
import os

import newrelic.agent

from django.core.wsgi import get_wsgi_application

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")

newrelic.agent.initialize(
    config_file=os.path.join(os.path.dirname(os.path.abspath(__file__)), 'newrelic.ini'),
    environment=os.environ.get('ENVIRONMENT', 'staging'))

application = get_wsgi_application()
application = newrelic.agent.WSGIApplicationWrapper(application)
