# -*- coding: utf-8 -*-
import os

from drf_yasg import openapi
from drf_yasg.inspectors import SwaggerAutoSchema
from drf_yasg.views import get_schema_view

from rest_framework import permissions
from rest_framework.decorators import api_view, permission_classes

from django.http import HttpResponse

class SwaggerAutoSchemaCustom(SwaggerAutoSchema):
    """ Custom SwaggerAutoSchema """

    def get_operation(self, operation_keys):
        consumes = self.get_consumes()

        body = self.get_request_body_parameters(consumes)
        query = self.get_query_parameters()
        parameters = body + query
        parameters = [param for param in parameters if param is not None]
        parameters = self.add_manual_parameters(parameters)

        operation_id = self.get_operation_id(operation_keys)
        summary, description = self.get_summary_and_description()

        remove_keys = ['create', 'update', 'delete', 'list', 'read', 'partial_update']
        operation_keys = [key for key in operation_keys if key not in remove_keys]

        if len(operation_keys) >= 2:
            tags = ["/".join([item for item in operation_keys])]
        else:
            tags = [operation_keys[-1]]

        responses = self.get_responses()

        return openapi.Operation(
            operation_id=operation_id,
            description=description,
            responses=responses,
            parameters=parameters,
            consumes=consumes,
            tags=tags,
        )

api_info = openapi.Info(
    title="Kontent.AI API",
    default_version='v1',
    description="",
    terms_of_service="",
    contact=openapi.Contact(email="<EMAIL>"),
    license=openapi.License(name="BSD License"),
)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def swagger_spec_view(request, format=None):
    file = f'{os.path.dirname(os.path.dirname(__file__))}/swagger.json'
    with open(file) as spec_file:
        file_contents = spec_file.read()
    return HttpResponse(file_contents, content_type="application/json")