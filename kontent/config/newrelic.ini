[newrelic]

license_key = 67401d43e8678e05b2bfa39dc26515cb0e7a63b8

app_name = Kontent API

monitor_mode = true

#log_file = /tmp/newrelic-python-agent.log

log_level = info

high_security = false

# proxy_scheme = http
# proxy_host = hostname
# proxy_port = 8080
# proxy_user =
# proxy_pass =

# attributes.include = request.parameters.*

transaction_tracer.enabled = true

transaction_tracer.transaction_threshold = apdex_f

transaction_tracer.record_sql = obfuscated

transaction_tracer.stack_trace_threshold = 0.5

transaction_tracer.explain_enabled = true

transaction_tracer.explain_threshold = 0.5

transaction_tracer.function_trace =

error_collector.enabled = true

error_collector.ignore_errors =

browser_monitoring.auto_instrument = true

thread_profiler.enabled = true

# api_key =

distributed_tracing.enabled = false

[newrelic:development]
monitor_mode = false

[newrelic:test]
monitor_mode = false

[newrelic:staging]
app_name = Kontent API (Staging)
monitor_mode = true

[newrelic:production]
app_name = Kontent API (Production)
monitor_mode = true
