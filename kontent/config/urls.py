"""
kontent URL Configuration
"""

from django.conf import settings
from django.conf.urls.static import static
from django.urls import path, include, re_path
from django.views.generic import RedirectView

from config.docs import swagger_spec_view
from learn_content.views.health_check import HealthCheckViewSet

urlpatterns = [
    re_path('^$', RedirectView.as_view(pattern_name='swagger-spec', permanent=True), name='swagger-spec'),
    path('swagger-spec/', swagger_spec_view, name='swagger-spec'),
    path('learn-content', include('learn_content.urls')),
    path('assessments', include('assessment_database.urls')),

    path('health-check', HealthCheckViewSet.as_view(), name='health-check'),
]

if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
