{"swagger": "2.0", "info": {"title": "Kontent.AI API", "description": "", "termsOfService": "", "contact": {"email": "<EMAIL>"}, "license": {"name": "BSD License"}, "version": "v1"}, "basePath": "/", "consumes": ["application/json"], "produces": ["application/json"], "securityDefinitions": {}, "paths": {"/assessments/answers": {"get": {"operationId": "assessments_answers_list", "description": "", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "type": "integer"}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"required": ["count", "results"], "type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": "string", "format": "uri", "x-nullable": true}, "previous": {"type": "string", "format": "uri", "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/Answer"}}}}}}, "tags": ["assessments/answers"]}, "post": {"operationId": "assessments_answers_create", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Answer"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/Answer"}}}, "tags": ["assessments/answers"]}, "parameters": []}, "/assessments/answers/{id}": {"delete": {"operationId": "assessments_answers_delete", "description": "", "parameters": [], "responses": {"204": {"description": ""}}, "tags": ["assessments/answers"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/assessments/exams": {"get": {"operationId": "assessments_exams_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "type": "integer"}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"required": ["count", "results"], "type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": "string", "format": "uri", "x-nullable": true}, "previous": {"type": "string", "format": "uri", "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/Exam"}}}}}}, "tags": ["assessments/exams"]}, "post": {"operationId": "assessments_exams_create", "description": "Create a new exam\n\n---\n    company_id => will be set by x-client in header\n    user_creator_id => will be set with user logged id\n    content_type => will be set as \"Question\" automatically.", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ExamPost"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/ExamPost"}}}, "tags": ["assessments/exams"]}, "parameters": []}, "/assessments/exams/{exam_id}/answers": {"get": {"operationId": "assessments_exams_answers_list", "description": "", "parameters": [{"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "type": "integer"}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"required": ["count", "results"], "type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": "string", "format": "uri", "x-nullable": true}, "previous": {"type": "string", "format": "uri", "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/Answer"}}}}}}, "tags": ["assessments/exams/answers"]}, "parameters": [{"name": "exam_id", "in": "path", "required": true, "type": "string"}]}, "/assessments/exams/{exam_id}/questions": {"get": {"operationId": "assessments_exams_questions_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "type": "integer"}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "type": "integer"}], "responses": {"200": {"description": ""}}, "tags": ["assessments/exams/questions"]}, "post": {"operationId": "assessments_exams_questions_create", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["assessments/exams/questions"]}, "parameters": [{"name": "exam_id", "in": "path", "required": true, "type": "string"}]}, "/assessments/exams/{id}": {"get": {"operationId": "assessments_exams_read", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/Exam"}}}, "tags": ["assessments/exams"]}, "put": {"operationId": "assessments_exams_update", "description": "Update (patch or put) the exam\n\n---\n    company_id => will be set by x-client in header\n    user_creator_id => will be set with user logged id\n    content_type => will be set as \"Question\" automatically.", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Exam"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/Exam"}}}, "tags": ["assessments/exams"]}, "patch": {"operationId": "assessments_exams_partial_update", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Exam"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/Exam"}}}, "tags": ["assessments/exams"]}, "delete": {"operationId": "assessments_exams_delete", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"204": {"description": ""}}, "tags": ["assessments/exams"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/assessments/questions": {"get": {"operationId": "assessments_questions_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "type": "integer"}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "type": "integer"}], "responses": {"200": {"description": ""}}, "tags": ["assessments/questions"]}, "post": {"operationId": "assessments_questions_create", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["assessments/questions"]}, "parameters": []}, "/assessments/questions/{id}": {"get": {"operationId": "assessments_questions_read", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["assessments/questions"]}, "put": {"operationId": "assessments_questions_update", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["assessments/questions"]}, "patch": {"operationId": "assessments_questions_partial_update", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["assessments/questions"]}, "delete": {"operationId": "assessments_questions_delete", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"204": {"description": ""}}, "tags": ["assessments/questions"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/assessments/questions/{question_id}/answers": {"get": {"operationId": "assessments_questions_answers_list", "description": "", "parameters": [{"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "type": "integer"}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"required": ["count", "results"], "type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": "string", "format": "uri", "x-nullable": true}, "previous": {"type": "string", "format": "uri", "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/Answer"}}}}}}, "tags": ["assessments/questions/answers"]}, "parameters": [{"name": "question_id", "in": "path", "required": true, "type": "string"}]}, "/health-check": {"get": {"operationId": "health-check_list", "description": "", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["health-check"]}, "parameters": []}, "/learn-content": {"get": {"operationId": "learn-content_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "type": "integer"}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "type": "integer"}], "responses": {"200": {"description": ""}}, "tags": ["learn-content"]}, "post": {"operationId": "learn-content_create", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["learn-content"]}, "parameters": []}, "/learn-content/categories": {"get": {"operationId": "learn-content_categories_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "type": "integer"}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"required": ["count", "results"], "type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": "string", "format": "uri", "x-nullable": true}, "previous": {"type": "string", "format": "uri", "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/ContentCategory"}}}}}}, "tags": ["learn-content/categories"]}, "post": {"operationId": "learn-content_categories_create", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ContentCategory"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/ContentCategory"}}}, "tags": ["learn-content/categories"]}, "parameters": []}, "/learn-content/categories/{id}": {"get": {"operationId": "learn-content_categories_read", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ContentCategory"}}}, "tags": ["learn-content/categories"]}, "put": {"operationId": "learn-content_categories_update", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ContentCategory"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ContentCategory"}}}, "tags": ["learn-content/categories"]}, "patch": {"operationId": "learn-content_categories_partial_update", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ContentCategory"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ContentCategory"}}}, "tags": ["learn-content/categories"]}, "delete": {"operationId": "learn-content_categories_delete", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"204": {"description": ""}}, "tags": ["learn-content/categories"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/learn-content/check-contents-analyzed": {"post": {"operationId": "learn-content_check-contents-analyzed_create", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CheckContentsAnalyzed"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/CheckContentsAnalyzed"}}}, "tags": ["learn-content/check-contents-analyzed"]}, "parameters": []}, "/learn-content/point-rules": {"get": {"operationId": "learn-content_point-rules_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "content_type__name", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "type": "integer"}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"required": ["count", "results"], "type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": "string", "format": "uri", "x-nullable": true}, "previous": {"type": "string", "format": "uri", "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/ContentPointRule"}}}}}}, "tags": ["learn-content/point-rules"]}, "post": {"operationId": "learn-content_point-rules_create", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ContentPointRule"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/ContentPointRule"}}}, "tags": ["learn-content/point-rules"]}, "parameters": []}, "/learn-content/point-rules/{id}": {"get": {"operationId": "learn-content_point-rules_read", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ContentPointRule"}}}, "tags": ["learn-content/point-rules"]}, "put": {"operationId": "learn-content_point-rules_update", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ContentPointRule"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ContentPointRule"}}}, "tags": ["learn-content/point-rules"]}, "patch": {"operationId": "learn-content_point-rules_partial_update", "description": "A viewset that provides the standard actions", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ContentPointRule"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ContentPointRule"}}}, "tags": ["learn-content/point-rules"]}, "delete": {"operationId": "learn-content_point-rules_delete", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"204": {"description": ""}}, "tags": ["learn-content/point-rules"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/learn-content/scorm": {"post": {"operationId": "learn-content_scorm_create", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["learn-content/scorm"]}, "parameters": []}, "/learn-content/transcribe-vocabulary": {"post": {"operationId": "learn-content_transcribe-vocabulary_create", "description": "", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["learn-content/transcribe-vocabulary"]}, "parameters": []}, "/learn-content/types": {"get": {"operationId": "learn-content_types_list", "description": "A viewset that provides the standard actions", "parameters": [{"name": "search", "in": "query", "description": "A search term.", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "type": "integer"}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"required": ["count", "results"], "type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": "string", "format": "uri", "x-nullable": true}, "previous": {"type": "string", "format": "uri", "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/ContentType"}}}}}}, "tags": ["learn-content/types"]}, "parameters": []}, "/learn-content/types/{id}": {"get": {"operationId": "learn-content_types_read", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ContentType"}}}, "tags": ["learn-content/types"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/learn-content/{id}": {"get": {"operationId": "learn-content_read", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["learn-content"]}, "put": {"operationId": "learn-content_update", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["learn-content"]}, "patch": {"operationId": "learn-content_partial_update", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["learn-content"]}, "delete": {"operationId": "learn-content_delete", "description": "A viewset that provides the standard actions", "parameters": [], "responses": {"204": {"description": ""}}, "tags": ["learn-content"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/learn-content/{id}/copy": {"post": {"operationId": "learn-content_copy_create", "description": "", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["learn-content/copy"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/swagger-spec/": {"get": {"operationId": "swagger-spec_list", "description": "", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["swagger-spec"]}, "parameters": []}}, "definitions": {"Answer": {"required": ["user_id", "enrollment_id", "options", "question"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "user_id": {"title": "User", "type": "string", "minLength": 1}, "app_id": {"title": "Application", "type": "string", "minLength": 1, "x-nullable": true}, "enrollment_id": {"title": "Enrollment", "type": "string", "minLength": 1}, "options": {"title": "Options", "type": "string", "minLength": 1}, "is_ok": {"title": "Is ok?", "type": "boolean"}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}, "question": {"title": "<PERSON><PERSON>", "type": "string", "format": "uuid"}}}, "Exam": {"required": ["title", "company_id", "user_creator_id", "content_type"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "title": {"title": "Title", "type": "string", "maxLength": 100, "minLength": 1}, "company_id": {"title": "Company ID", "type": "string", "minLength": 1}, "user_creator_id": {"title": "User ID", "type": "string", "minLength": 1}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}, "content_type": {"title": "Type", "type": "string", "format": "uuid"}}}, "ExamPost": {"required": ["title"], "type": "object", "properties": {"title": {"title": "Title", "type": "string", "maxLength": 100, "minLength": 1}}}, "ContentCategory": {"required": ["name", "image"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}, "description": {"title": "Description", "type": "string", "x-nullable": true}, "image": {"title": "Pulse Type Image", "type": "string", "format": "uri", "maxLength": 200, "minLength": 1}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}}}, "CheckContentsAnalyzed": {"required": ["learn_content_ids", "application_name", "callback_id"], "type": "object", "properties": {"learn_content_ids": {"type": "array", "items": {"type": "string", "minLength": 1}}, "application_name": {"title": "Application name", "type": "string", "enum": ["KONQUEST"]}, "callback_id": {"title": "Callback id", "type": "string", "minLength": 1}}}, "ContentPointRule": {"required": ["points", "unit", "quantity"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "points": {"title": "Points", "type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "unit": {"title": "Unit", "type": "string", "maxLength": 100, "minLength": 1}, "quantity": {"title": "Quantity", "type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}, "content_type": {"required": ["name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}, "description": {"title": "Description", "type": "string", "x-nullable": true}, "image": {"title": "Content Type Image", "type": "string", "format": "uri", "maxLength": 200, "x-nullable": true}, "image_cover": {"title": "Content Type Image Cover", "type": "string", "format": "uri", "maxLength": 200, "x-nullable": true}, "extensions": {"title": "Extensions", "type": "string", "maxLength": 500, "x-nullable": true}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}}, "readOnly": true}}}, "ContentType": {"required": ["name"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "minLength": 1}, "description": {"title": "Description", "type": "string", "x-nullable": true}, "image": {"title": "Content Type Image", "type": "string", "format": "uri", "maxLength": 200, "x-nullable": true}, "image_cover": {"title": "Content Type Image Cover", "type": "string", "format": "uri", "maxLength": 200, "x-nullable": true}, "extensions": {"title": "Extensions", "type": "string", "maxLength": 500, "x-nullable": true}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time", "readOnly": true}, "updated_date": {"title": "Updated Date", "type": "string", "format": "date-time", "readOnly": true}}}}}