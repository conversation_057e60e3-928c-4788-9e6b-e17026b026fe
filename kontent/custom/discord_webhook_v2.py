import logging
from datetime import datetime

import requests


class DiscordWebhookLoggerV2(logging.Handler):
    def __init__(self, webhook_url):
        super().__init__()
        self.webhook_url = webhook_url

    def emit(self, record):
        log_entry = self.format(record)
        payload = {
            "username": "Kontent",
            "content": "A new error has occurred in the Kontent application.",
            "embeds": [
                {
                    "title": "Error in Kontent Application",
                    "description": log_entry,
                    "color": 16711680,
                    "fields": [
                        {
                            "name": "Log Level",
                            "value": record.levelname,
                            "inline": True,
                        },
                        {"name": "Module", "value": record.module, "inline": True},
                        {
                            "name": "Timestamp",
                            "value": datetime.now().isoformat() + "Z",
                            "inline": False,
                        },
                    ],
                    "footer": {
                        "text": "Django Error Logs",
                        "icon_url": "https://cdn-icons-png.flaticon.com/512/1827/1827400.png",
                    },
                }
            ],
        }
        try:
            response = requests.post(self.webhook_url, json=payload)
            response.raise_for_status()
        except requests.exceptions.RequestException as e:
            print(f"Failed to send log to Discord: {e}")
