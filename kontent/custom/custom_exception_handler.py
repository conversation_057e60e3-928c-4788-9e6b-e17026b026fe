# -*- coding: utf-8 -*-
import logging
from django.db import IntegrityError
from jose import ExpiredSignatureError
from rest_framework.response import Response
from rest_framework.views import exception_handler
from rest_framework.exceptions import ValidationError

from custom.exceptions.kp_permission_error import KPPermissionError
from custom.exceptions.kp_service_error import KPServiceError
from custom.keeps_exception_handler import KeepsError

logger = logging.getLogger(__name__)
apm_logger = logging.getLogger('apm')


def custom_exception_handler(exc, context):

    # Call REST framework's default exception handler first,
    # to get the standard error response.
    response = exception_handler(exc, context)

    if isinstance(exc, KPServiceError):
        return Response({"i18n": exc.msg, "detail": exc.description}, status=400)

    if isinstance(exc, KPPermissionError):
        return Response(
            {"i18n": exc.msg, "detail": exc.description},
            status=403,
        )

    # Now add the HTTP status code to the response.
    if isinstance(exc, ValidationError):
        response = Response({'detail': ''.join(str(e) for e in exc.detail), 'status_code': 400},
                            status=400)

    elif isinstance(exc, KeepsError):
        return Response({'i18n': exc.i18n, 'detail': exc.detail, 'status_code': exc.status_code},
                        status=exc.status_code)

    elif response is not None:
        response.data['status_code'] = response.status_code

    elif isinstance(exc, ExpiredSignatureError):
        response = Response({'detail': str(exc), 'status_code': 401},
                            status=401)

    elif isinstance(exc, IntegrityError):
        return Response({'i18n': 'integrity_error', 'detail': str(exc), 'status_code': 409}, status=409)

    elif isinstance(exc, Exception):
        logger.error('Internal Server Error', exc_info=True)
        apm_logger.error('Internal Server Error', exc_info=True)
        response = Response({'detail': str(exc), 'status_code': 500}, status=500)

    return response
