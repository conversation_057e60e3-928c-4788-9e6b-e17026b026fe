from django.utils.translation import gettext_noop
from django.utils.translation import gettext as _

from custom.exceptions.kp_service_error import KPServiceError

UNABLE_TO_EDIT_QUESTION_WITH_ANSWERS = gettext_noop("unable_to_edit_question_with_answers")


class KPUnableToEditQuestionWithAnswers(KPServiceError):
    def __init__(self):
        super().__init__(UNABLE_TO_EDIT_QUESTION_WITH_ANSWERS, _(UNABLE_TO_EDIT_QUESTION_WITH_ANSWERS))
