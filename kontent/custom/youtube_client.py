import logging
import re
from urllib.parse import parse_qs, urlparse

import isodate
from custom.keeps_exception_handler import KeepsBadRequestError
from django.conf import settings
from googleapiclient.discovery import build
from youtube_transcript_api import (
    NoTranscriptFound,
    TranscriptsDisabled,
    YouTubeTranscriptApi,
)

logger = logging.getLogger("kontent_log")


class YoutubeClient:
    def info(self, url):
        logger.debug(f"Fetching information for video URL: {url}")
        api_key = settings.GOOGLE_YOUTUBE_API_KEY

        if not api_key:
            logger.error("GOOGLE_YOUTUBE_API_KEY is not set. Cannot proceed with request.")
            raise EnvironmentError("GOOGLE_YOUTUBE_API_KEY is not set. Please provide a valid Youtube API KEY.")

        youtube = build("youtube", "v3", developerKey=api_key)
        video_id = self._extract_video_id(url)

        request = youtube.videos().list(part="snippet,contentDetails", id=video_id)
        response = request.execute()

        if response["items"]:
            video_data = response["items"][0]
            description = video_data["snippet"]["description"]
            duration_iso = video_data["contentDetails"]["duration"]
            duration_timedelta = isodate.parse_duration(duration_iso)
            duration_seconds = duration_timedelta.total_seconds()
            tags = video_data["snippet"].get("tags", [])
            language = video_data["snippet"].get("defaultAudioLanguage", "No language specified")
            return {
                "duration": duration_seconds,
                "description": description,
                "tags": tags,
                "language": language,
            }
        else:
            raise KeepsBadRequestError(i18n="video_unavailable", detail="video unavailable")

    def transcript(self, url, language):
        language = "pt" if language == "pt-BR" else language

        video_id = self._extract_video_id(url)
        try:
            transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=[language])
            transcript_text = " ".join(entry["text"] for entry in transcript)
            return transcript_text
        except NoTranscriptFound:
            logger.warning(f"No transcript found for video ID: {video_id}")
            return "Transcript not available"
        except TranscriptsDisabled:
            logger.warning(f"Could not retrieve a transcript for video ID: {video_id}")
            return "Could not retrieve a transcript"
        except Exception as e:
            logger.error(f"An error occurred while fetching the transcript: {str(e)}")
            return ""

    def _extract_video_id(self, url):
        parsed_url = urlparse(url)

        if "youtu.be" in parsed_url.netloc:
            video_id = parsed_url.path[1:]
            return video_id.rstrip("!")

        elif "youtube.com" in parsed_url.netloc:
            query_params = parse_qs(parsed_url.query)
            video_id = query_params.get("v", [None])[0]
            if video_id:
                return video_id.rstrip("!")

        video_id_regex = re.compile(r"(?:v=|\/)([0-9A-Za-z_-]{11})(?:\?|&|$)")
        match = video_id_regex.search(url)
        if match:
            return match.group(1).rstrip("!")

        return None
