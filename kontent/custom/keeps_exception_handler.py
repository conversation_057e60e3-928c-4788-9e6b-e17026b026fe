from rest_framework.exceptions import APIException
from rest_framework import status


# pylint: disable=super-init-not-called
class KeepsError(APIException):

    def __init__(self, detail, i18n, status_code):
        self.i18n = i18n
        self.detail = detail
        self.status_code = status_code


class KeepsBadRequestError(KeepsError):

    def __init__(self, detail, i18n):
        self.i18n = i18n
        self.detail = detail
        self.status_code = status.HTTP_400_BAD_REQUEST


class KeepsInvalidFileUrl(KeepsBadRequestError):

    def __init__(self, detail="Invalid File Url", i18n="invalid_file_url"):
        self.i18n = i18n
        self.detail = detail
        self.status_code = status.HTTP_400_BAD_REQUEST


class KeepsGDriveDocumentNotFound(KeepsBadRequestError):

    def __init__(self, detail="Document is not an shareable link", i18n="gdrive_document_not_shareable"):
        self.i18n = i18n
        self.detail = detail
        self.status_code = status.HTTP_400_BAD_REQUEST


class KeepsNotAllowedError(KeepsError):

    def __init__(self, detail, i18n):
        self.i18n = i18n
        self.detail = detail
        self.status_code = status.HTTP_403_FORBIDDEN


class KeepsClientHeaderNotFoundCompanyError(KeepsError):

    def __init__(self):
        self.i18n = 'x_client_not_found'
        self.detail = 'Company not found (set x-client on header)'
        self.status_code = status.HTTP_403_FORBIDDEN


class KeepsNotFoundCompanyError(KeepsError):

    def __init__(self):
        self.i18n = 'company_not_found'
        self.detail = 'Company not found, check if service is active'
        self.status_code = status.HTTP_404_NOT_FOUND


class KeepsNotAllowedAccessCompanyError(KeepsError):

    def __init__(self):
        self.i18n = 'not_allowed_access_company_resources'
        self.detail = 'You do not have permission to access data for this company'
        self.status_code = status.HTTP_403_FORBIDDEN
