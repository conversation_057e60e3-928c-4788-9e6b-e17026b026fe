from custom.disable_options_method_middleware import DisableOptionsMethodMiddleware
from django.http import HttpResponse
from django.test import RequestFactory, TestCase
from django.urls import include, path
from rest_framework.response import Response
from rest_framework.routers import Default<PERSON>outer
from rest_framework.viewsets import ViewSet


class TestViewSet(ViewSet):
    def list(self, request):
        return Response({"message": "GET method allowed"})

    def create(self, request):
        return Response({"message": "POST method allowed"})


router = DefaultRouter()
router.register(r'test', TestViewSet, basename='test')

urlpatterns = [
    path('', include(router.urls)),
]


class DisableOptionsMethodMiddlewareTest(TestCase):
    def setUp(self):
        self.factory = RequestFactory()
        self.get_response = lambda request: HttpResponse()
        self.middleware = DisableOptionsMethodMiddleware(self.get_response)

    def test_options_method_not_allowed(self):
        request = self.factory.options('/test/')
        response = self.middleware(request)
        self.assertEqual(response.status_code, 405)

    def test_get_method_allowed(self):
        request = self.factory.get('/test/')
        response = self.middleware(request)
        self.assertEqual(response.status_code, 200)

    def test_post_method_allowed(self):
        request = self.factory.post('/test/')
        response = self.middleware(request)
        self.assertEqual(response.status_code, 200)
