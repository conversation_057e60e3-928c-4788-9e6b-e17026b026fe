from unittest.mock import MagicMock, patch

from custom.youtube_client import YoutubeClient
from django.test import TestCase
from youtube_transcript_api import NoTranscriptFound


class YoutubeClientTestCase(TestCase):

    def setUp(self):
        self.youtube_client = YoutubeClient()
        self.video_url = "https://www.youtube.com/watch?v=QJ_HlzA6WWs"
        self.transcript_url = "https://www.youtube.com/watch?v=QJ_HlzA6WWs"
        self.shortened_url = "https://youtu.be/QJ_HlzA6WWs"
        self.url_with_exclamation = "https://www.youtube.com/watch?v=QJ_HlzA6WWs!"
        self.shortened_url_with_params = "https://youtu.be/QJ_HlzA6WWs?feature=shared"

    @patch("custom.youtube_client.build")
    @patch("django.conf.settings.GOOGLE_YOUTUBE_API_KEY", "fake_api_key")
    def test_info_returns_video_data(self, mock_build):
        # Simulação do cliente do YouTube
        mock_youtube = MagicMock()
        mock_videos = mock_youtube.videos.return_value
        mock_list = mock_videos.list.return_value
        mock_list.execute.return_value = {
            "items": [
                {
                    "snippet": {
                        "description": "Test video description",
                        "tags": ["test", "video"],
                        "defaultAudioLanguage": "en",
                    },
                    "contentDetails": {"duration": "PT10M30S"},
                }
            ]
        }

        mock_build.return_value = mock_youtube

        result = self.youtube_client.info(self.video_url)

        self.assertEqual(result["description"], "Test video description")
        self.assertEqual(result["duration"], 630.0)
        self.assertEqual(result["tags"], ["test", "video"])
        self.assertEqual(result["language"], "en")

    @patch("custom.youtube_client.build")
    @patch("django.conf.settings.GOOGLE_YOUTUBE_API_KEY", None)
    def test_info_raises_environment_error(self, mock_build):
        with self.assertRaises(EnvironmentError):
            self.youtube_client.info(self.video_url)

    @patch("youtube_transcript_api.YouTubeTranscriptApi.get_transcript")
    def test_transcript_returns_transcript_text(self, mock_get_transcript):
        mock_get_transcript.return_value = [
            {"text": "Hello world"},
            {"text": "This is a test transcript"},
        ]

        result = self.youtube_client.transcript(self.transcript_url, "en")

        self.assertEqual(result, "Hello world This is a test transcript")

    @patch("youtube_transcript_api.YouTubeTranscriptApi.get_transcript")
    def test_transcript_no_transcript_found(self, mock_get_transcript):
        mock_get_transcript.side_effect = NoTranscriptFound(None, None, None)

        result = self.youtube_client.transcript(self.transcript_url, "en")

        self.assertEqual(result, "Transcript not available")

    @patch("youtube_transcript_api.YouTubeTranscriptApi.get_transcript")
    def test_transcript_handles_exception(self, mock_get_transcript):
        mock_get_transcript.side_effect = Exception("Some error")

        result = self.youtube_client.transcript(self.transcript_url, "en")

        self.assertEqual(result, "")

    def test_extract_video_id_youtube_url(self):
        video_id = self.youtube_client._extract_video_id(self.video_url)
        self.assertEqual(video_id, "QJ_HlzA6WWs")

    def test_extract_video_id_youtu_be_url(self):
        video_id = self.youtube_client._extract_video_id(self.shortened_url)
        self.assertEqual(video_id, "QJ_HlzA6WWs")

    def test_extract_video_id_with_exclamation(self):
        video_id = self.youtube_client._extract_video_id(self.url_with_exclamation)
        self.assertEqual(video_id, "QJ_HlzA6WWs")

    def test_extract_video_id_shortened_url_with_parameters(self):
        video_id = self.youtube_client._extract_video_id(self.shortened_url_with_params)
        self.assertEqual(video_id, "QJ_HlzA6WWs")

    def test_extract_video_id_invalid_url(self):
        video_id = self.youtube_client._extract_video_id("https://invalid.url")
        self.assertIsNone(video_id)
