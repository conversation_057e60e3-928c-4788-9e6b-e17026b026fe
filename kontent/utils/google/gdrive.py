from __future__ import print_function
from urllib.parse import urlparse
from uuid import uuid4
import requests
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload

from custom.discord_webhook import DiscordWebhookLogger
from custom.keeps_exception_handler import KeepsGDriveDocumentNotFound

MIME_TYPE_DOC_FROM = "application/msword"
MIME_TYPE_DOC_TO = "application/vnd.google-apps.document"
MIME_TYPE_DOCX_FROM = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
MIME_TYPE_DOCX_TO = "application/vnd.google-apps.document"
MIME_TYPE_PPT_FROM = "application/vnd.openxmlformats-officedocument.presentationml.presentation"
MIME_TYPE_PPT_TO = "application/vnd.google-apps.presentation"
MIME_TYPE_PPTX_FROM = "application/vnd.openxmlformats-officedocument.presentationml.presentation"
MIME_TYPE_PPTX_TO = "application/vnd.google-apps.presentation"
MIME_TYPE_XLS_FROM = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
MIME_TYPE_XLS_TO = "application/vnd.google-apps.spreadsheet"
MIME_TYPE_XLSX_FROM = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
MIME_TYPE_XLSX_TO = "application/vnd.google-apps.spreadsheet"


class GoogleDrive:
    """
    Integrate with Google drive to upload and convert files as:
    Word(doc, docx), Excel(xls, xlsx) and Power Point(ppt, pptx)

    Google Drive Documentation:
    - https://developers.google.com/resources/api-libraries/documentation/drive/v3/python/latest/drive_v3.files.html
    - https://developers.google.com/drive/api/v3/about-sdk
    - https://developers.google.com/drive/api/v3/reference/

    """
    def __init__(self, credentials, base_dir, webhook_logger: DiscordWebhookLogger):
        self.scopes = [
            'https://www.googleapis.com/auth/drive.file',
            'https://www.googleapis.com/auth/drive',
            'https://www.googleapis.com/auth/drive.file',
            'https://www.googleapis.com/auth/drive.metadata'
        ]
        self.mime_types = [
            ('doc', MIME_TYPE_DOC_FROM, MIME_TYPE_DOC_TO),
            ('docx', MIME_TYPE_DOCX_FROM, MIME_TYPE_DOCX_TO),
            ('ppt', MIME_TYPE_PPT_FROM, MIME_TYPE_PPT_TO),
            ('pptx', MIME_TYPE_PPTX_FROM, MIME_TYPE_PPTX_TO),
            ('xls', MIME_TYPE_XLS_FROM, MIME_TYPE_XLS_TO),
            ('xlsx', MIME_TYPE_XLSX_FROM, MIME_TYPE_XLSX_TO)
        ]
        self.credentials = credentials
        self.base_dir = base_dir
        self.webhook_logger = webhook_logger

    def setup_service(self):
        credentials_file = self.credentials
        credentials = service_account.Credentials.from_service_account_file(credentials_file, scopes=self.scopes)
        service = build('drive', 'v3', credentials=credentials)

        return service

    def files_extensions_allowed(self):
        return [x for (x, y, z) in self.mime_types]

    def map_mime_types(self, file_type):
        return [(x, y, z) for (x, y, z) in self.mime_types if x == file_type][0]

    def list_documents(self, page_size=10):
        service = self.setup_service()
        results = service.files().list(pageSize=page_size, fields="nextPageToken, files(id, name)").execute()

        return results

    def send_file(self, file, name, file_type):
        service = self.setup_service()
        media = MediaFileUpload(file, mimetype=self.map_mime_types(file_type)[1], resumable=True)
        file_metadata = {'name': 'My Report', 'mimeType': self.map_mime_types(file_type)[2]}
        file = service.files().create(body=file_metadata, media_body=media, fields='id,webViewLink').execute()
        anyone_permission = {'type': 'anyone', 'role': 'reader'}
        service.permissions().create(fileId=file['id'], body=anyone_permission, fields='id').execute()
        url_parsed = urlparse(file['webViewLink'])
        url = '{}://{}{}'.format(url_parsed.scheme, url_parsed.hostname, url_parsed.path.replace('/edit', ''))

        return {"file_id": file['id'], "url": url}

    def download_file(self, file_id, file_type):
        self.check_public_document(file_id)
        local_file_download = '{}/temp/{}.{}'.format(self.base_dir, str(uuid4()), file_type)
        url_download_pattern = {
            'html': "https://docs.google.com/document/d/{}/export/docx",
            'pptx': "https://docs.google.com/presentation/d/{}/export/pptx",
            'xlsx': "https://docs.google.com/spreadsheets/d/{}/export/xlsx"
        }
        url = url_download_pattern[file_type].format(file_id)
        data = requests.get(url, allow_redirects=True)  # pylint: disable=missing-timeout
        if data:
            # pylint: disable-msg=consider-using-with
            open(local_file_download, 'wb').write(data.content)  # noqa: SIM115

        return local_file_download

    def check_public_document(self, file_id):
        service = self.setup_service()
        if not file_id:
            raise KeepsGDriveDocumentNotFound()
        try:
            service.files().get(fileId=file_id).execute()
        except Exception as e:
            raise KeepsGDriveDocumentNotFound() from e

    @staticmethod
    def get_confirm_token(response):
        for key, value in response.cookies.items():
            if key.startswith('download_warning'):
                return value

        return None

    @staticmethod
    def save_response_content(response, destination):
        chunk_size = 32768
        with open(destination, "wb") as destination_file:
            for chunk in response.iter_content(chunk_size):
                if chunk:
                    destination_file.write(chunk)
