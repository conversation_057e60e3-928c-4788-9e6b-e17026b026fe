from .aws import aws_s3  # noqa: F401
from .aws import aws_transcribe  # noqa: F401
from .google import gcp_transcribe  # noqa: F401
from .nlp import summarize  # noqa: F401

import re


def format_file_name(file_name: str) -> str:
    """
    Args:
        file_name (str): The original file name.

    Returns:
        str: A formatted file name.
    """
    file_name = file_name.replace(" ", "_")
    file_name = re.sub(r"[^\w.-]", "", file_name)
    file_name = file_name.lower()
    file_name = file_name.strip("._")

    return file_name
