from django.test import TestCase
import os

from config.settings import BASE_DIR
from utils.pdf_compressor import compress


class TestPDFCompressor(TestCase):
    def setUp(self) -> None:
        self.file_name = f"{BASE_DIR}/utils/tests/files/sample.pdf"
        self.file_output_name = f"{BASE_DIR}/utils/tests/files/compressed.pdf"

    def test_compress(self) -> None:
        initial_size = os.path.getsize(self.file_name)
        compress(self.file_name, self.file_output_name)
        compressed_size = os.path.getsize(self.file_output_name)
        assert compressed_size < initial_size
