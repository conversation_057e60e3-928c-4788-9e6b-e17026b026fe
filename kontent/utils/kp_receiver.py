import functools

from django.dispatch import receiver

from django.conf import settings


def kp_receiver(signal, **decorator_kwargs):
    def our_wrapper(func):
        @receiver(signal, **decorator_kwargs)
        @functools.wraps(func)
        def fake_receiver(sender, **kwargs):
            if settings.SUSPEND_SIGNALS:
                return None
            return func(sender, **kwargs)

        return fake_receiver

    return our_wrapper
