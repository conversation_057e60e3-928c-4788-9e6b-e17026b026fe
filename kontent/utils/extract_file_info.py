import os
import mimetypes
from urllib.parse import urlparse


def extract_file_format(file_path_or_url: str):
    """
    Extract the file format (extension) from either a file path or a URL.

    Args:
        file_path_or_url (str): The path to the file or a URL.

    Returns:
        str: The file extension or mime type if the extension is available.
    """
    if file_path_or_url.startswith('http://') or file_path_or_url.startswith('https://'):
        file_name = os.path.basename(urlparse(file_path_or_url).path)
    else:
        file_name = os.path.basename(file_path_or_url)

    file_extension = os.path.splitext(file_name)[1].lower()

    if not file_extension:
        mime_type, _ = mimetypes.guess_type(file_path_or_url)
        return mime_type if mime_type else 'unknown/unknown'

    return file_extension.lstrip('.')


def get_file_size(input_file: str) -> float:
    """
    Get the size of the file in MB.

    Args:
        input_file (str): The path to the input file.

    Returns:
        float: The size of the file in MB.
    """
    file_size = os.path.getsize(input_file) / (1024 * 1024)  # Convert bytes to MB
    return round(file_size, 2)
