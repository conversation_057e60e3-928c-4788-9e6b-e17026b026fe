from contextlib import contextmanager

import django
from django.conf import settings

from di import Container


# todo: Normalizar injeção de depedência utilizada no projeto seguindo o padrão do Konquest ()
@contextmanager
def task_transaction(method_name: str):
    container = Container()

    try:
        yield container
    except Exception as error:
        if settings.TEST:
            raise error
        logger = container.webhook_logger
        logger.emit_short_message(f"{method_name.replace('_', ' ')} - Worker", error)
    finally:
        if not settings.TEST:
            django.db.connections.close_all()
