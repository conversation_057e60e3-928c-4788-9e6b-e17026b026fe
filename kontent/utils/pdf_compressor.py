from PDFNetPython3.PDFNetPython import PDFDoc, Optimizer, SDFDoc, PDFNet

from config import settings


def compress(input_file: str, output_file: str = None):
    """Compress PDF file"""
    if not output_file:
        output_file = input_file
    # Initialize the library
    PDFNet.Initialize(settings.PDFTRON_KEY)
    doc = PDFDoc(input_file)
    # Optimize PDF with the default settings
    doc.InitSecurityHandler()
    # Reduce PDF size by removing redundant information and compressing data streams
    Optimizer.Optimize(doc)
    doc.Save(output_file, SDFDoc.e_linearized)
    doc.Close()
