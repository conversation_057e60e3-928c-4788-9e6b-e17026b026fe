import logging

import requests

# Get an instance of a logger
from config.settings import KONQUEST_API_URL, KEEPS_SECRET_TOKEN_INTEGRATION, KONQUEST_WEB_URL

logger = logging.getLogger(__name__)


# pylint: disable=missing-timeout
# pylint: disable=logging-format-interpolation
# pylint: disable=consider-using-in
def send_konquest_notification(user_id, message):
    logger.info('SEND_KONQUEST_NOTIFICATION_FOR: {}'.format(user_id))

    response = requests.post(KONQUEST_API_URL + "notifications",
                             headers={'Authorization': KEEPS_SECRET_TOKEN_INTEGRATION},
                             data={
                                 "user_receiving": user_id,
                                 "notification_type": "504d72e7-dfaf-4f46-97a6-58d51e71c2cb",
                                 "message": message,
                                 "link": KONQUEST_WEB_URL,
                                 "read": False
                             })

    if response.status_code != 201 and response.status_code != 200:
        logger.error("ERROR_SEND_KONQUEST_NOTIFICATION: {} ({}) - {}".format(response.url, response.status_code, response.text))

    return response.text
