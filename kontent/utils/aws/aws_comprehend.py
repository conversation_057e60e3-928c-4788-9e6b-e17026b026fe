import re
import boto3


class AwsComprehendClient:

    def __init__(self, aws_access_key, aws_secret_key, aws_region):
        self._client = boto3.client('comprehend',
                                    aws_access_key_id=aws_access_key,
                                    aws_secret_access_key=aws_secret_key,
                                    region_name=aws_region)

    def dominant_language(self, text):
        return self._client.detect_dominant_language(Text=text[0:2000])

    def analyse_sentiment(self, text_to_analyze, language_code):
        return self._client.detect_key_phrases(Text=text_to_analyze, LanguageCode=language_code)

    def key_phrases(self, text_to_analyze, language_code):
        keys_phrases = []
        batch = 2000
        if len(text_to_analyze) < 2000:
            keys_phrases = self._client.detect_key_phrases(Text=text_to_analyze,
                                                           LanguageCode=language_code)['KeyPhrases']
        else:
            for i in range(int(len(text_to_analyze) / batch)):
                keys_phrases = keys_phrases + (self._client.detect_key_phrases(
                    Text=text_to_analyze[i * batch:(i + 1) * batch],
                    LanguageCode=language_code)['KeyPhrases']
                )

        return keys_phrases

    def entities(self, text_to_analyze, language_code):
        entities = []
        batch = 2000
        if len(text_to_analyze) < 2000:
            entities = self._client.detect_entities(Text=text_to_analyze,
                                                    LanguageCode=language_code)['Entities']
        else:
            for i in range(int(len(text_to_analyze) / batch)):
                entities = entities + (self._client.detect_entities(
                    Text=text_to_analyze[i * batch:(i + 1) * batch],
                    LanguageCode=language_code)['Entities']
                )

        return entities

    # pylint: disable-msg=invalid-name
    @staticmethod
    def generate_tags(keys):
        tags = []
        for key_word in keys:
            if key_word['Score'] >= 0.9:
                tx = re.sub(r'\d+', '', key_word['Text'])
                tx = re.sub(r'\s+', ' ', tx).strip().lower()
                if len(tx) > 3 and tx not in tags:
                    tags.append({"text": tx, "relevance": key_word['Score']})

        return tags
