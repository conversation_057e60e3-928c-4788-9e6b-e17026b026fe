import uuid
import mimetypes

import boto3
from boto3.s3.transfer import TransferConfig
from unidecode import unidecode

FILE_TYPES = {
    "video": ["mp4", "webm", "ogg"],
    "audio": ["m4a", "mp3", "wav"],
    "image": ["png", "jpg", "jpeg", "gif"],
}


# pylint: disable=not-callable
# pylint: disable=too-many-instance-attributes
# pylint: disable=import-outside-toplevel
class AmazonS3:

    def __init__(self, access_key, secret_access, region_name, default_bucket, base_url, temp_folder):
        self.access_key = access_key
        self.secrete_access = secret_access
        self.region_name = region_name
        self.bucket_name = default_bucket
        self.base_public = base_url
        self.temp_folder = temp_folder

        self.resource = boto3.resource('s3', aws_access_key_id=self.access_key,
                                       aws_secret_access_key=self.secrete_access,
                                       region_name=self.region_name)
        self.client = boto3.client('s3', aws_access_key_id=self.access_key, aws_secret_access_key=self.secrete_access,
                                   region_name=self.region_name)

    @staticmethod
    def file_types():
        return FILE_TYPES

    def send_file(self, file_name, file_path, bucket=None, sub_folder='general', workspace_id: str = None) -> dict:
        """
        Upload file to AWS S3

        :param file_name: string
        :param file_path: where file uploaded
        :param sub_folder: account or company identity (uuid or name)
        :param workspace_id: company identity (uuid or name)
        :param bucket: bucket name

        :return {name: string, url: string}:

        """
        key = unidecode(file_name).replace(" ", '_')
        if sub_folder:
            key = f'{unidecode(sub_folder).replace(" ", "_")}/{key}'
        if workspace_id:
            key = f'clients/{unidecode(workspace_id).replace(" ", "_")}/{key}'

        if not bucket:
            bucket = self.bucket_name

        config = TransferConfig(
            multipart_threshold=64 * 1024 * 1024,
            max_concurrency=10,
            num_download_attempts=10,
            multipart_chunksize=16 * 1024 * 1024,
            max_io_queue=10000
        )

        mime_type, _encoding = mimetypes.guess_type(file_path)

        self.client.upload_file(file_path, bucket, key,
                                Config=config, ExtraArgs={'ACL': 'public-read', 'ContentType': mime_type})

        response = {
            'name': key,
            'url': '{}/{}/{}'.format(self.base_public, bucket, key),
            'file_mime_type': mime_type,
        }

        return response

    def delete_object(self, bucket, key):
        self.client.delete_object(Bucket=bucket, Key=key)

    def send_object(self, fileobj, bucket, bucket_path, mime_type):
        self.resource.meta.client.upload_fileobj(
            fileobj,
            Bucket=bucket,
            Key=bucket_path,
            ExtraArgs={'ContentType': mime_type, 'ACL': 'public-read'})

        response = {
            'name': fileobj,
            'url': '{}/{}/{}'.format(self.base_public, bucket, bucket_path)
        }
        return response

    def list_files(self, client='default'):
        s3_instance = self.resource()
        file_type = None

        files = []

        for s3_file in s3_instance.Bucket(self.bucket_name).objects.filter(Prefix=client):

            for types in FILE_TYPES:
                file_extension = str(s3_file.key.split('/')[1]).rsplit('.', 1)[1]

                if file_extension in FILE_TYPES[types]:
                    file_type = types

            files.append({
                'name': s3_file.key.split('/')[1],
                'url': 'https://s3.amazonaws.com/{}/{}'.format(self.bucket_name, s3_file.key),
                'date': str(s3_file.last_modified),
                'size': s3_file.size,
                'type': file_type
            })

        return files

    def delete_file(self, file_name, bucket):
        response = self.client.delete_object(
            Bucket=bucket,
            Key=file_name,
        )
        return response

    def download(self, key, bucket=None):
        extension = key.split('.').pop()
        file_name = f"{self.temp_folder}/{uuid.uuid4()}.{extension}"
        if not bucket:
            bucket = self.bucket_name
        self.resource.Bucket(bucket).download_file(key, file_name)
        return file_name

    def download_by_url(self, url: str) -> str:
        bucket, key = self.extract_s3_url_bucket_key(url)
        return self.download(key, bucket)

    @staticmethod
    def extract_s3_url_bucket_key(url: str) -> tuple:
        from urllib.parse import urlparse
        parse_result = urlparse(url)
        bucket, key = parse_result.path.split('/', 2)[1:]
        return bucket, key
