import logging
from urllib.parse import urlparse

import requests
from django.conf import settings

logger = logging.getLogger("kontent_log")


class VimeoInfoExtractor:
    def get_info_content(self, video_url):
        logger.debug(f"Fetching information for video URL: {video_url}")
        api_key = settings.VIMEO_TOKEN
        if not api_key:
            logger.error(
                "VIMEO_TOKEN is not set. Cannot proceed with Vimeo API request."
            )
            raise EnvironmentError(
                "VIMEO_TOKEN is not set. Please provide a valid Vimeo API token."
            )

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Referer": settings.REFERER_VIMEO_EMBEDDED,
        }

        # Making the request to the Vimeo API
        try:
            url = f"https://vimeo.com/api/oembed.json?url={video_url}"
            response = requests.get(url, headers=headers)
            response.raise_for_status()  # Throws an error for HTTP 4xx/5xx status
        except requests.exceptions.RequestException as e:
            raise ConnectionError(f"Error when making the request to Vimeo API: {e}")

        # Parsing the API response
        try:
            video_data = response.json()
            logger.debug(f"Received data from Vimeo API: {video_data}")
        except ValueError:
            logger.error(
                f"Error processing the Vimeo API response. Status code: {response.status_code}"
            )
            raise ValueError(
                f"Error processing the Vimeo API data. The response is not valid JSON. Status code: {response.status_code}"
            )

        # Checking if the necessary fields are present
        if "duration" not in video_data or "description" not in video_data:
            logger.error(
                f"Vimeo API response missing 'duration' or 'description'. Full response: {video_data}"
            )
            raise KeyError(
                f"Error: The Vimeo API response does not contain 'duration' or 'description' fields. Response: {video_data}"
            )

        # Ensuring description is not None before calling .strip()
        description = video_data.get("description", "")
        if description:
            description = description.strip().replace("'", "")
        else:
            """ logger.warning(
                f"No description found for video ID {video_id}, setting to empty string."
            ) """
            description = ""

        return {"duration": video_data.get("duration"), "description": description}

    def _extract_vimeo_id(self, url):
        try:
            parsed_url = urlparse(url)
            video_id = parsed_url.path.split("/")[-1]

            # Check if the video ID consists only of digits
            if video_id.isdigit():
                logger.debug(
                    f"Successfully extracted video ID {video_id} from URL: {url}"
                )
                return video_id
            else:
                logger.warning(
                    f"Failed to extract a valid numeric video ID from URL: {url}"
                )
                return None
        except Exception as e:
            logger.error(f"Error extracting video ID from URL: {url}. Details: {e}")
            raise ValueError(f"Error extracting video ID from URL: {url}. Details: {e}")
