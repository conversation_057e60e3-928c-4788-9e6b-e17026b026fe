# -*- coding: utf-8 -*-

from django.urls import path

from assessment_database.views.answer_viewset import AnswerViewSet, AnswerExamViewSet, AnswerQuestionViewSet
from assessment_database.views.exam_viewset import ExamViewSet
from assessment_database.views.question_viewset import QuestionViewSet

_read_only = {'get': 'list'}

_list = {'get': 'list',
         'post': 'create'}

_detail = {'get': 'retrieve',
           'put': 'update',
           'patch': 'partial_update',
           'delete': 'destroy'}

_only_delete = {
    'delete': 'destroy'
}
urlpatterns = [
    path('/exams', ExamViewSet.as_view(_list), name='exams-list'),
    path('/exams/<uuid:pk>', ExamViewSet.as_view(_detail), name='exams-detail'),
    path('/exams/<uuid:exam_id>/questions', QuestionViewSet.as_view(_list), name='exams-questions-list'),
    path('/exams/<uuid:exam_id>/answers', AnswerExamViewSet.as_view(_read_only), name='answers-exam-list'),

    path('/questions', QuestionViewSet.as_view(_list), name='questions-list'),
    path('/questions/<uuid:pk>', QuestionViewSet.as_view(_detail), name='questions-detail'),
    path('/questions/<uuid:question_id>/answers', AnswerQuestionViewSet.as_view(_read_only), name='answers-question-list'),

    path('/answers', AnswerViewSet.as_view(_list), name='answers'),
    path('/answers/<uuid:pk>', AnswerViewSet.as_view(_only_delete), name='answer-delete'),

]
