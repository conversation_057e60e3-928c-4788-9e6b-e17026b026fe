import uuid
from django.urls import reverse
from django.test import TestCase
from model_mommy import mommy

from rest_framework.test import APIClient

from assessment_database.models import Exam, Question, ExamQuestion, QuestionOption


class ExamViewSetTestCase(TestCase):
    fixtures = ['content_category', 'content_point_rule', 'content_type']

    def setUp(self):
        self.client = APIClient()
        self.user_uuid = 'afde09d3-b33c-413a-b956-ee412af857ce'
        self.company_uuid1 = 'd21ad25f-50f4-4c67-9291-1c11b636eb81'
        self.company_uuid2 = 'd21ad25f-50f4-4c67-9291-1c11b636eb82'

        self.client.force_authenticate(user={'sub': self.user_uuid, 'client_id': self.company_uuid1})

        self.exam_1 = mommy.make(Exam, id=uuid.uuid4(), company_id=self.company_uuid1)
        self.exam_2 = mommy.make(Exam, id=uuid.uuid4(), company_id=self.company_uuid2)

        self.question_1 = mommy.make(Question, company_id=self.company_uuid1, points=10)
        self.question_1_1 = mommy.make(Question, company_id=self.company_uuid1, points=10)
        self.question_2 = mommy.make(Question, company_id=self.company_uuid2, points=10)

        self.exam_question_1 = mommy.make(ExamQuestion, exam=self.exam_1, question=self.question_1, order=2)
        self.exam_question_1_1 = mommy.make(ExamQuestion, exam=self.exam_1, question=self.question_1_1, order=1)
        self.exam_question_2 = mommy.make(ExamQuestion, exam=self.exam_2, question=self.question_2)

        self.option_1 = mommy.make(QuestionOption, question=self.question_1)
        self.option_2 = mommy.make(QuestionOption, question=self.question_1)
        self.option_3 = mommy.make(QuestionOption, question=self.question_2)
        self.option_4 = mommy.make(QuestionOption, question=self.question_2)

        self.headers = {'HTTP_X_CLIENT': str(self.company_uuid1)}
        self.url = reverse('exams-list')

    def test_exams_list(self):
        response = self.client.get(self.url, **self.headers, format='json').json()

        self.assertEqual(len(response['results']), 1)

    def test_exams_detail_company_1(self):
        url_detail = reverse('exams-detail', args=[str(self.exam_1.id)])
        response = self.client.get(url_detail, **self.headers, format='json').json()

        self.assertEqual(len(response['questions']), 2)
        self.assertEqual(len(response['questions'][0]['options']), 0)
        self.assertEqual(len(response['questions'][1]['options']), 2)
        self.assertEqual(response['questions'][1]['options'][0]['id'], str(self.option_1.id))
        self.assertEqual(response['questions'][1]['options'][1]['id'], str(self.option_2.id))

        self.assertEqual(response['title'], self.exam_1.title)
        self.assertEqual(response['points'], 20)
        self.assertEqual(response['company_id'], self.company_uuid1)

    def test_exams_detail_company_2(self):
        self.client.force_authenticate(user={'sub': self.user_uuid, 'client_id': self.company_uuid2})
        headers = {'HTTP_X_CLIENT': str(self.company_uuid2)}
        url_detail = reverse('exams-detail', args=[str(self.exam_2.id)])
        response = self.client.get(url_detail, **headers, format='json').json()

        self.assertEqual(len(response['questions']), 1)
        self.assertEqual(len(response['questions'][0]['options']), 2)
        self.assertEqual(response['questions'][0]['options'][0]['id'], str(self.option_3.id))
        self.assertEqual(response['questions'][0]['options'][1]['id'], str(self.option_4.id))

        self.assertEqual(response['title'], self.exam_2.title)
        self.assertEqual(response['points'], 10)
        self.assertEqual(response['company_id'], self.company_uuid2)

    def test_exams_detail_company_2_cant_access_company_1(self):
        self.client.force_authenticate(user={'sub': self.user_uuid, 'client_id': self.company_uuid2})
        headers = {'HTTP_X_CLIENT': str(self.company_uuid2)}
        url_detail = reverse('exams-detail', args=[str(self.exam_1.id)])
        response = self.client.get(url_detail, **headers, format='json').json()

        self.assertEqual(response['status_code'], 404)
        self.assertEqual(response['detail'], 'Not found.')

    def test_exams_get_not_found(self):
        response = self.client.get(reverse('exams-detail', args=[str(uuid.uuid4())]),
                                   **self.headers, format='json')
        response_json = response.json()

        self.assertEqual(response_json['detail'], 'Not found.')
        self.assertEqual(response.status_code, 404)

    def test_exams_post_success(self):
        data = {
            "title": "Test"
        }

        response = self.client.post(self.url, **self.headers, data=data, format='json')
        response_json = response.json()

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response_json['title'], data['title'])
        self.assertEqual(response_json['content_type'], '7a41a8e0-ee37-4d0b-ad4f-35bada67134d')
        self.assertEqual(response_json['user_creator_id'], self.user_uuid)
        self.assertEqual(response_json['user_creator_id'], self.user_uuid)
        self.assertEqual(response_json['company_id'], self.company_uuid1)

    def test_exams_post_required_field(self):

        data = {
        }

        response = self.client.post(self.url, **self.headers, data=data, format='json')
        response_json = response.json()

        self.assertEqual(response_json['detail'], 'title')
        self.assertEqual(response.status_code, 400)

    def test_exams_patch_success(self):

        data = {
            "title": 'New title',
        }

        response = self.client.patch(reverse('exams-detail', args=[str(self.exam_1.id)]),
                                     **self.headers, data=data, format='json')
        response_json = response.json()

        self.assertEqual(response_json['title'], data['title'])
        self.assertEqual(response.status_code, 200)

    def test_exams_put_success(self):

        data = {
            "title": "New title",
        }

        response = self.client.put(reverse('exams-detail', args=[str(self.exam_1.id)]),
                                   **self.headers, data=data, format='json')
        response_json = response.json()

        self.assertEqual(response_json['title'], data['title'])
        self.assertEqual(response.status_code, 200)

    def test_exams_delete_success(self):
        """
        Can delete an Exam but their question not
        """
        response = self.client.delete(reverse('exams-detail', args=[str(self.exam_1.id)]), **self.headers, format='json')
        questions_company_1 = Question.objects.filter(company_id=self.company_uuid1).count()

        self.assertEqual(response.status_code, 204)
        self.assertEqual(questions_company_1, 2)

    def test_exams_and_questions_post_success(self):
        new_exam = mommy.make(Exam, id=uuid.uuid4(), company_id=self.company_uuid1)
        url = reverse('exams-questions-list', args=[str(new_exam.id)])

        data = {
            "title": "Test",
            "question_type": "correct_choices",
            "order": 1,
            "options": [{
                "option": "bla",
                "correct_answer": True
            },
                {
                    "option": "bla bla",
                    "correct_answer": False
                }]
        }

        response = self.client.post(url, **self.headers, data=data, format='json')
        response_json = response.json()
        exam_question = ExamQuestion.objects.filter(exam_id=str(new_exam.id)).first()

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response_json['user_creator_id'], self.user_uuid)
        self.assertEqual(response_json['company_id'], self.company_uuid1)

        self.assertEqual(exam_question.exam_id, new_exam.id)
        self.assertEqual(str(exam_question.question_id), response_json['id'])

    def test_exams_and_questions_get_success(self):
        exam = mommy.make(Exam, id=uuid.uuid4(), company_id=self.company_uuid1)
        question_1 = mommy.make(Question, company_id=self.company_uuid1)
        question_2 = mommy.make(Question, company_id=self.company_uuid1)
        mommy.make(ExamQuestion, exam=exam, question=question_1)
        mommy.make(ExamQuestion, exam=exam, question=question_2)

        url = reverse('exams-questions-list', args=[str(exam.id)])

        response = self.client.get(url, **self.headers, format='json')
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response_json['results']), 2)
        self.assertEqual(response_json['results'][0]['id'], str(question_1.id))
        self.assertEqual(response_json['results'][1]['id'], str(question_2.id))

