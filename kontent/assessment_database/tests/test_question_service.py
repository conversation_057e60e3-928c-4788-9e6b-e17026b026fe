import uuid

import pytest
from django.test import TestCase
from django.contrib.auth.models import User

from model_mommy import mommy

from assessment_database.models import Question, QuestionOption, ExamQuestion, Answer
from custom.exceptions.kp_not_allowed_to_edit_question import KPNotAllowedToEditQuestion
from custom.exceptions.kp_unable_to_edit_question_already_answered import KPUnableToEditQuestionWithAnswers
from learn_content.models import ContentPointRule, ContentType
from assessment_database.services.question_service import QuestionService


class QuestionServiceTestCase(TestCase):

    def setUp(self):
        self.user = {"sub": uuid.uuid4()}

        # Create a mock ContentPointRule
        self.content_type = mommy.make(ContentType, name='Question')
        self.content_point_rule = mommy.make(ContentPointRule, content_type=self.content_type, points=5, quantity=1)

        self.service = QuestionService(self.user, 'client123')

    def test_create_question_with_options(self):
        question_data = {
            'title': 'Sample Question',
            'question_type': 'correct_choices'
        }
        options_data = [
            {'option': 'Option 1', 'correct_answer': True},
            {'option': 'Option 2', 'correct_answer': False},
        ]

        question = self.service.create_question(validated_data=question_data, options_data=options_data)

        self.assertEqual(question.title, 'Sample Question')
        self.assertEqual(question.question_type, 'correct_choices')
        self.assertEqual(question.points, self.content_point_rule.points)

        options = QuestionOption.objects.filter(question=question)
        self.assertEqual(options.count(), 2)
        self.assertEqual(options[0].option, 'Option 1')
        self.assertTrue(options[0].correct_answer)
        self.assertEqual(options[1].option, 'Option 2')
        self.assertFalse(options[1].correct_answer)

    def test_create_question_without_options(self):
        question_data = {
            'title': 'Sample Question No Options',
            'question_type': 'correct_essay'
        }

        question = self.service.create_question(validated_data=question_data)

        # Test the created question
        self.assertEqual(question.title, 'Sample Question No Options')
        self.assertEqual(question.question_type, 'correct_essay')
        self.assertEqual(question.points, self.content_point_rule.points)  # Points should be set to the default

        # Test there are no options
        options = QuestionOption.objects.filter(question=question)
        self.assertEqual(options.count(), 0)

    def test_update_question_with_options(self):
        question = Question.objects.create(
            title='Old Question',
            question_type='correct_essay',
            company_id='client123',
            user_creator_id='creator123',
            points=10
        )

        update_data = {
            'title': 'Updated Question',
            'question_type': 'correct_essay'
        }
        update_options_data = [
            {'option': 'Updated Option 1', 'correct_answer': True},
            {'option': 'Updated Option 2', 'correct_answer': False},
        ]

        updated_question = self.service.update_question(
            question, validated_data=update_data, options_data=update_options_data
        )

        # Test the updated question
        self.assertEqual(updated_question.title, 'Updated Question')
        self.assertEqual(updated_question.question_type, 'correct_essay')
        self.assertEqual(updated_question.points, 10)  # Points should remain unchanged

        # Test the updated options
        options = QuestionOption.objects.filter(question=updated_question)
        self.assertEqual(options.count(), 2)
        self.assertEqual(options[0].option, 'Updated Option 1')
        self.assertTrue(options[0].correct_answer)
        self.assertEqual(options[1].option, 'Updated Option 2')
        self.assertFalse(options[1].correct_answer)

    def test_unable_to_edit_question_with_answers(self):
        question = mommy.make(Question, title='Old Question', company_id='client123')
        question_data = {
            'title': 'Sample Question No Options'
        }

        mommy.make(Answer, question=question)

        with pytest.raises(KPUnableToEditQuestionWithAnswers):
            question = self.service.update_question(question, validated_data=question_data)

        question.refresh_from_db()
        self.assertEqual(question.title, 'Old Question')

    def test_not_allowed_to_edit_the_question(self):
        question = mommy.make(Question, title='Old Question', company_id='anotherCompanyID')
        question_data = {
            'title': 'Sample Question No Options'
        }

        with pytest.raises(KPNotAllowedToEditQuestion):
            question = self.service.update_question(question, validated_data=question_data)

        question.refresh_from_db()
        self.assertEqual(question.title, 'Old Question')

    def test_link_question_to_exam(self):
        question = mommy.make(
            Question,
            title='Test Question for Exam',
            question_type='correct_choices',
            company_id='client123',
            user_creator_id='creator123',
            points=10
        )
        exam_id = uuid.uuid4()

        self.service.link_to_exam(exam_id, question.id, order=1)

        # Verify the link exists in the ExamQuestion model
        exam_question = ExamQuestion.objects.get(exam_id=exam_id, question_id=question.id)
        self.assertEqual(exam_question.exam_id, exam_id)
        self.assertEqual(exam_question.question_id, question.id)
        self.assertEqual(exam_question.order, 1)

    def tearDown(self):
        Question.objects.all().delete()
        QuestionOption.objects.all().delete()
        ExamQuestion.objects.all().delete()
        User.objects.all().delete()
        ContentPointRule.objects.all().delete()
