import uuid
from django.urls import reverse
from django.test import TestCase
from model_mommy import mommy

from rest_framework.test import APIClient

from assessment_database.models import Question, QuestionOption, Answer, Exam, ExamQuestion
from learn_content.models import ContentPointRule


class AnswerViewSetTestCase(TestCase):
    fixtures = ['content_category', 'content_point_rule', 'content_type']

    def setUp(self):
        self.client = APIClient()
        self.user_uuid = 'afde09d3-b33c-413a-b956-ee412af857ce'
        self.company_uuid1 = 'd21ad25f-50f4-4c67-9291-1c11b636eb81'
        self.company_uuid2 = 'd21ad25f-50f4-4c67-9291-1c11b636eb82'
        self.points_default = ContentPointRule.objects.filter(content_type__name='Question').first().points

        self.client.force_authenticate(user={'sub': self.user_uuid, 'client_id': self.company_uuid1})

        self.exam = mommy.make(Exam, id=uuid.uuid4(), company_id=self.company_uuid1)
        self.question_1 = mommy.make(Question, company_id=self.company_uuid1, user_creator_id=self.user_uuid)
        self.question_2 = mommy.make(Question, company_id=self.company_uuid1, user_creator_id=self.user_uuid)
        self.exam_question_1 = mommy.make(ExamQuestion, exam=self.exam, question=self.question_1)
        self.exam_question_2 = mommy.make(ExamQuestion, exam=self.exam, question=self.question_2)

        self.exam2 = mommy.make(Exam, id=uuid.uuid4(), company_id=self.company_uuid2)
        self.question_3 = mommy.make(Question, company_id=self.company_uuid2, user_creator_id=self.user_uuid)
        self.question_4 = mommy.make(Question, company_id=self.company_uuid2, user_creator_id=self.user_uuid)
        self.exam_question_3 = mommy.make(ExamQuestion, exam=self.exam2, question=self.question_3)
        self.exam_question_4 = mommy.make(ExamQuestion, exam=self.exam2, question=self.question_4)

        self.option_11 = mommy.make(QuestionOption, question=self.question_1)
        self.option_12 = mommy.make(QuestionOption, question=self.question_1)
        self.option_21 = mommy.make(QuestionOption, question=self.question_2)
        self.option_22 = mommy.make(QuestionOption, question=self.question_2)
        self.option_31 = mommy.make(QuestionOption, question=self.question_3)
        self.option_32 = mommy.make(QuestionOption, question=self.question_3)
        self.option_41 = mommy.make(QuestionOption, question=self.question_4)
        self.option_42 = mommy.make(QuestionOption, question=self.question_4)

        self.answer_1 = mommy.make(Answer, question=self.question_1, user_id=self.user_uuid)
        self.answer_2 = mommy.make(Answer, question=self.question_2, user_id=self.user_uuid)
        self.answer_3 = mommy.make(Answer, question=self.question_3, user_id=self.user_uuid)
        self.answer_4 = mommy.make(Answer, question=self.question_4, user_id=self.user_uuid)

        self.headers = {'HTTP_X_CLIENT': str(self.company_uuid1)}

    def test_answers_from_exam_list(self):
        url = reverse('answers-exam-list', args=[str(self.exam.id)])
        response = self.client.get(url, **self.headers, format='json').json()
        self.assertEqual(len(response['results']), 2)
        self.assertEqual(response['results'][0]['question'], str(self.question_1.id))
        self.assertEqual(response['results'][1]['question'], str(self.question_2.id))

    def test_answers_from_question_list(self):
        url = reverse('answers-question-list', args=[str(self.question_2.id)])
        response = self.client.get(url, **self.headers, format='json').json()
        self.assertEqual(len(response['results']), 1)
        self.assertEqual(response['results'][0]['question'], str(self.question_2.id))

    def test_answers_from_exam_cant_list_from_company2(self):
        url = reverse('answers-exam-list', args=[str(self.exam2.id)])
        response = self.client.get(url, **self.headers, format='json').json()
        self.assertEqual(response['detail'], 'You do not have permission to access data for this company')

    def test_answers_from_question_cant_list_from_company2(self):
        url = reverse('answers-question-list', args=[str(self.question_3.id)])
        response = self.client.get(url, **self.headers, format='json').json()
        self.assertEqual(response['detail'], 'You do not have permission to access data for this company')

    def test_answer_post_success(self):
        """
        Test post answer using user logged (token) as user_id (user answered)
        """
        self.client.force_authenticate(user={'sub': "654321", 'client_id': self.company_uuid1})
        url = reverse('answers')

        data = {
            "questions":
            [
                {
                    "id": str(self.question_1.id),
                    "options": [str(self.option_12.id)]
                }
            ]
        }

        response = self.client.post(url, **self.headers, data=data, format='json')
        self.assertEqual(response.status_code, 201)

    def test_answer_post_with_user_in_body_success(self):
        """
        Test post answer using user_id on body request (user answered)
        """
        self.headers = {'HTTP_X_CLIENT': str(self.company_uuid1)}

        url = reverse('answers')

        data = {

            "user_id": "123456",
            "enrollment_id": "654321",
            "app_id": "smartzap",
            "questions":
            [
                {
                    "id": str(self.question_1.id),
                    "options": [
                        str(self.option_12.id),
                    ]
                }
            ]
        }

        response = self.client.post(url, **self.headers, data=data, format='json')
        self.assertEqual(response.status_code, 201)

    def test_answers_delete(self):
        question = mommy.make(Question, company_id=self.company_uuid1, user_creator_id=self.user_uuid)
        answer = mommy.make(Answer, question=question, user_id=self.user_uuid)
        url = reverse('answer-delete', args=[str(answer.id)])
        response = self.client.delete(url, **self.headers, format='json')
        self.assertEqual(response.status_code, 204)

    def test_answer_post_user_question_enrollment_together_restrictions(self):
        self.headers = {'HTTP_X_CLIENT': str(self.company_uuid1)}
        question = mommy.make(Question, company_id=self.company_uuid1, user_creator_id=self.user_uuid)
        option = mommy.make(QuestionOption, question=question)
        url = reverse('answers')

        data = {

            "user_id": "123456",
            "enrollment_id": "654321",
            "app_id": "smartzap",
            "questions":
                [
                    {
                        "id": str(question.id),
                        "options": [str(option.id)]
                    }
                ]
        }
        response = self.client.post(url, **self.headers, data=data, format='json')
        self.assertEqual(response.status_code, 201)

        data = {

            "user_id": "123456",
            "enrollment_id": "654321",
            "app_id": "smartzap",
            "questions":
                [
                    {
                        "id": str(question.id),
                        "options": [str(option.id)]
                    }
                ]
        }
        response = self.client.post(url, **self.headers, data=data, format='json')
        self.assertEqual(response.status_code, 409)
