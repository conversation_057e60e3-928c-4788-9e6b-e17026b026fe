import uuid
from django.urls import reverse
from django.test import TestCase
from model_mommy import mommy

from rest_framework.test import APIClient

from assessment_database.models import Question, QuestionOption
from learn_content.models import ContentPointRule


class QuestionViewSetTestCase(TestCase):
    fixtures = ['content_category', 'content_point_rule', 'content_type']

    def setUp(self):
        self.client = APIClient()
        self.user_uuid = 'afde09d3-b33c-413a-b956-ee412af857ce'
        self.company_uuid1 = 'd21ad25f-50f4-4c67-9291-1c11b636eb81'
        self.company_uuid2 = 'd21ad25f-50f4-4c67-9291-1c11b636eb82'
        self.points_default = ContentPointRule.objects.filter(content_type__name='Question').first().points

        self.client.force_authenticate(user={'sub': self.user_uuid, 'client_id': self.company_uuid1})

        self.question_1 = mommy.make(Question, company_id=self.company_uuid1, user_creator_id=self.user_uuid)
        self.question_2 = mommy.make(Question, company_id=self.company_uuid2, user_creator_id=self.user_uuid)

        self.option_1 = mommy.make(QuestionOption, question=self.question_1)
        self.option_2 = mommy.make(QuestionOption, question=self.question_1)
        self.option_3 = mommy.make(QuestionOption, question=self.question_2)
        self.option_4 = mommy.make(QuestionOption, question=self.question_2)

        self.headers = {'HTTP_X_CLIENT': str(self.company_uuid1)}
        self.url = reverse('questions-list')

    def test_question_list(self):
        response = self.client.get(self.url, **self.headers, format='json').json()
        self.assertEqual(len(response['results']), 1)

    def test_questions_detail_company_1(self):
        url_detail = reverse('questions-detail', args=[str(self.question_1.id)])
        response = self.client.get(url_detail, **self.headers, format='json').json()

        self.assertEqual(len(response['options']), 2)
        self.assertEqual(response['options'][0]['id'], str(self.option_1.id))
        self.assertEqual(response['options'][1]['id'], str(self.option_2.id))

        self.assertEqual(response['title'], self.question_1.title)
        self.assertEqual(response['company_id'], self.company_uuid1)
        self.assertEqual(response['user_creator_id'], self.user_uuid)

    def test_questions_detail_company_2(self):
        self.client.force_authenticate(user={'sub': self.user_uuid, 'client_id': self.company_uuid2})
        headers = {'HTTP_X_CLIENT': str(self.company_uuid2)}
        url_detail = reverse('questions-detail', args=[str(self.question_2.id)])
        response = self.client.get(url_detail, **headers, format='json').json()

        self.assertEqual(len(response['options']), 2)
        self.assertEqual(response['options'][0]['id'], str(self.option_3.id))
        self.assertEqual(response['options'][1]['id'], str(self.option_4.id))

        self.assertEqual(response['title'], self.question_2.title)
        self.assertEqual(response['company_id'], self.company_uuid2)
        self.assertEqual(response['user_creator_id'], self.user_uuid)

    def test_questions_detail_company_2_cant_access_company_1(self):
        self.client.force_authenticate(user={'sub': self.user_uuid, 'client_id': self.company_uuid2})
        headers = {'HTTP_X_CLIENT': str(self.company_uuid2)}
        url_detail = reverse('questions-detail', args=[str(self.question_1.id)])
        response = self.client.get(url_detail, **headers, format='json').json()

        self.assertEqual(response['status_code'], 404)
        self.assertEqual(response['detail'], 'Not found.')

    def test_questions_get_not_found(self):
        response = self.client.get(reverse('questions-detail', args=[str(uuid.uuid4())]),
                                   **self.headers, format='json')
        response_json = response.json()

        self.assertEqual(response_json['detail'], 'Not found.')
        self.assertEqual(response.status_code, 404)

    def test_questions_post_success(self):
        data = {
            "title": "Test",
            "question_type": "correct_choices"
        }

        response = self.client.post(self.url, **self.headers, data=data, format='json')
        response_json = response.json()

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response_json['user_creator_id'], self.user_uuid)
        self.assertEqual(response_json['company_id'], self.company_uuid1)
        self.assertEqual(response_json['points'], self.points_default)

    def test_questions_post_with_questions_success(self):
        data = {
            "title": "Test",
            "question_type": "correct_choices",
            "options": [
                {
                    "option": "bla",
                    "correct_answer": True
                },
                {
                    "option": "bla bla",
                    "correct_answer": False
                }
            ]
        }

        response = self.client.post(self.url, **self.headers, data=data, format='json')
        response_json = response.json()
        options_count = QuestionOption.objects.filter(question=response_json['id']).count()

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response_json['user_creator_id'], self.user_uuid)
        self.assertEqual(response_json['company_id'], self.company_uuid1)
        self.assertEqual(response_json['points'], self.points_default)
        self.assertEqual(response_json['options'][0]['option'], data['options'][0]['option'])
        self.assertEqual(response_json['options'][0]['correct_answer'], data['options'][0]['correct_answer'])
        self.assertEqual(response_json['options'][1]['option'], data['options'][1]['option'])
        self.assertEqual(response_json['options'][1]['correct_answer'], data['options'][1]['correct_answer'])
        self.assertEqual(options_count, 2)

    def test_questions_post_required_field(self):
        data = {
            "title": "Test"
        }

        response = self.client.post(self.url, **self.headers, data=data, format='json')
        response_json = response.json()

        self.assertEqual(response_json['detail'], 'question_type')
        self.assertEqual(response.status_code, 400)

    def test_questions_patch_success(self):
        data = {
            "title": 'New title',
        }

        response = self.client.patch(reverse('questions-detail', args=[str(self.question_1.id)]),
                                     **self.headers, data=data, format='json')
        response_json = response.json()

        self.assertEqual(response_json['title'], data['title'])
        self.assertEqual(response.status_code, 200)

    def test_questions_put_success(self):
        data = {
            "title": "New title",
            "question_type": "correct_choices"
        }

        response = self.client.put(reverse('questions-detail', args=[str(self.question_1.id)]),
                                   **self.headers, data=data, format='json')
        response_json = response.json()

        self.assertEqual(response_json['title'], data['title'])
        self.assertEqual(response.status_code, 200)

    def test_questions_delete_success(self):
        response = self.client.delete(reverse('questions-detail', args=[str(self.question_1.id)]), **self.headers, format='json')
        options_question_1 = QuestionOption.objects.filter(question=self.question_1).count()

        self.assertEqual(response.status_code, 204)
        self.assertEqual(options_question_1, 0)
