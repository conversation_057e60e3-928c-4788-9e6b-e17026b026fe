# -*- coding: utf-8 -*-
from rest_framework import serializers
from django.db.models import Sum, Case, When
from assessment_database.models import Question, Answer
from assessment_database.models.exam import Exam
from assessment_database.serializers.answer_serializer import AnswerSerializer
from assessment_database.serializers.question_serializer import QuestionAndOptionsSerializer


class ExamPostSerializer(serializers.ModelSerializer):

    class Meta:
        model = Exam
        fields = ['title']


class ExamSerializer(serializers.ModelSerializer):

    class Meta:
        model = Exam
        fields = '__all__'


class ExamDetailSerializer(serializers.ModelSerializer):
    questions = serializers.SerializerMethodField()
    points = serializers.SerializerMethodField()
    user_answers = serializers.SerializerMethodField()

    @staticmethod
    def get_questions(obj):
        questions = obj.examquestion_set.all()
        if not questions.exists():
            return []
        questions = questions.order_by('order').values_list('question_id', flat=True)
        preserved = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(questions)])
        instance = Question.objects.filter(id__in=questions).order_by(preserved)
        return QuestionAndOptionsSerializer(instance, many=True).data

    @staticmethod
    def get_points(obj):
        questions = obj.examquestion_set.all().values_list('question_id', flat=True)
        points = Question.objects.filter(id__in=questions).aggregate(Sum('points'))['points__sum']
        return points

    def get_user_answers(self, obj):
        questions = obj.examquestion_set.values_list('question_id', flat=True)

        query = Answer.objects.filter(question_id__in=questions)

        if self.context['request'].GET.get('user_id'):
            user_id = self.context['request'].GET.get('user_id')
            query = query & Answer.objects.filter(user_id=user_id)
        elif 'sub' in self.context['request'].user:
            user_id = self.context['request'].user['sub']
            query = query & Answer.objects.filter(user_id=user_id)

        if self.context['request'].GET.get('enrollment_id'):
            enrollment_id = self.context['request'].GET.get('enrollment_id')
            query = query & Answer.objects.filter(enrollment_id=enrollment_id)

        return AnswerSerializer(query.all(), many=True).data

    class Meta:
        model = Exam
        fields = '__all__'
        depth = 1
