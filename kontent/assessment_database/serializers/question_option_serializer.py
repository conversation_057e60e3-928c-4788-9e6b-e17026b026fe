# -*- coding: utf-8 -*-

from rest_framework import serializers

from assessment_database.models import QuestionOption


class QuestionOptionSerializer(serializers.ModelSerializer):

    class Meta:
        model = QuestionOption
        fields = ('id', 'option', 'correct_answer')


class QuestionOptionListSerializer(serializers.ModelSerializer):

    class Meta:
        model = QuestionOption
        fields = ('id', 'option', 'correct_answer')
        depth = 1
