# -*- coding: utf-8 -*-
from rest_framework import serializers

from assessment_database.models import Question
from assessment_database.serializers.question_option_serializer import QuestionOptionSerializer


class QuestionSerializer(serializers.ModelSerializer):

    class Meta:
        model = Question
        fields = '__all__'


class QuestionAndOptionsSerializer(serializers.ModelSerializer):
    options = QuestionOptionSerializer(many=True, read_only=True)

    class Meta:
        model = Question
        fields = '__all__'


class QuestionAndOptionsSaveSerializer(serializers.ModelSerializer):
    options = QuestionOptionSerializer(many=True, required=False)

    class Meta:
        model = Question
        fields = ('question_type', 'options', 'title')
