import uuid
from unittest.mock import MagicMock

from django.test import <PERSON><PERSON><PERSON>
from model_mommy import mommy
from requests import Request

from assessment_database.models import Exam
from assessment_database.serializers.exam_serializer import ExamDetailSerializer


class TestExamDetailSerializer(TestCase):
    def test_should_serializer_without_question(self):
        exam = mommy.make(Exam)
        request = MagicMock(Request)
        request.GET = {"user_id": uuid.uuid4()}
        assert ExamDetailSerializer(exam, context={"request": request}).data
