# -*- coding: utf-8 -*-

import uuid
from django.db import models

from assessment_database.models.exam import Exam
from assessment_database.models.question import Question


class ExamQuestion(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    exam = models.ForeignKey(Exam, verbose_name='Type', on_delete=models.CASCADE, null=False, blank=False)
    question = models.ForeignKey(Question, verbose_name='Type', on_delete=models.CASCADE, null=False, blank=False)
    order = models.IntegerField(verbose_name="Order", null=False, blank=False, default=1)

    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True)

    class Meta:
        verbose_name_plural = "Exam Question"
        db_table = 'exam_question'
        unique_together = ('exam', 'question')
