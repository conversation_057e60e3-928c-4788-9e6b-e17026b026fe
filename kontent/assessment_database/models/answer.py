# -*- coding: utf-8 -*-

import uuid

from django.db import models
from assessment_database.models.question import Question


class Answer(models.Model):
    """
    Save the user answer.

    field option is to save te text of option, even multiple-choice or essay questions
    field is_ok save if user assert question correctly
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    question = models.ForeignKey(Question, verbose_name="Exam Has Question", null=True, on_delete=models.SET_NULL)
    user_id = models.TextField(verbose_name="User")
    app_id = models.TextField(verbose_name="Application", null=True)
    enrollment_id = models.TextField(verbose_name="Enrollment", null=True)

    options = models.TextField(verbose_name="Options")
    is_ok = models.BooleanField(verbose_name="Is ok?", default=False)

    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True)

    class Meta:
        verbose_name_plural = "Answers"
        db_table = "answer"
        unique_together = ('question', 'user_id', 'enrollment_id')
