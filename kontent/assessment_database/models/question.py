# -*- coding: utf-8 -*-

import uuid

from django.core.exceptions import ValidationError
from django.db import models

QUESTION_TYPE_CHOICES = (
    ("correct_choices", "correct_choices"),
    ("correct_essay", "correct_essay"),
    ("correct_fill_the_blank_order", "correct_fill_the_blank_order"),
)


class Question(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.TextField(verbose_name="Question Title", null=False, blank=False, unique=False)
    question_type = models.CharField(verbose_name="Question Type", max_length=50, choices=QUESTION_TYPE_CHOICES)
    points = models.IntegerField(verbose_name="Points")

    company_id = models.TextField(verbose_name="Company ID")
    user_creator_id = models.TextField(verbose_name="User ID")

    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True)

    def update(self, **kwargs):
        """
        Update the attributes of the object with the given keyword arguments.

        :param kwargs: Dictionary of attribute names and their values.
        :raises ValidationError: If an attribute does not exist in the object.
        """
        for name, value in kwargs.items():
            if hasattr(self, name):
                setattr(self, name, value)
            else:
                raise ValidationError(f"Field '{name}' does not exist in the object.")

    class Meta:
        verbose_name_plural = "Questions"
        db_table = "question"
