# -*- coding: utf-8 -*-

import uuid

from django.db import models

from learn_content.models.content_type import ContentType


class Exam(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(verbose_name="Title", max_length=100)
    company_id = models.TextField(verbose_name="Company ID")
    user_creator_id = models.TextField(verbose_name="User ID")
    content_type = models.ForeignKey(ContentType, verbose_name='Type', on_delete=models.PROTECT, null=False)

    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True)

    class Meta:
        verbose_name_plural = "Exam"
        db_table = "exam"
