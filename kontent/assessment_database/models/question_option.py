# -*- coding: utf-8 -*-

import uuid
from django.db import models

from assessment_database.models.question import Question


class QuestionOption(models.Model):
    """
    correct answer for multiple-choice and essay
    correct answer text for essay questions
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    option = models.TextField(verbose_name="Option", null=False, blank=False, unique=False)
    correct_answer = models.BooleanField(default=False)
    correct_answer_text = models.TextField(verbose_name="Option Text", null=True, blank=True)

    question = models.ForeignKey(
        Question, verbose_name='Type', on_delete=models.CASCADE, null=False, blank=False, related_name="options"
    )

    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True)

    class Meta:
        verbose_name_plural = "Question Options"
        db_table = 'question_option'
        unique_together = ('question', 'option')
