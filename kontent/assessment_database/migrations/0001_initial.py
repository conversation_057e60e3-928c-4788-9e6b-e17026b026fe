# Generated by Django 2.2 on 2022-04-15 22:25

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('learn_content', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Exam',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=100, verbose_name='Title')),
                ('company_id', models.TextField(verbose_name='Company ID')),
                ('user_creator_id', models.TextField(verbose_name='User ID')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='Updated Date')),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='learn_content.ContentType', verbose_name='Type')),
            ],
            options={
                'verbose_name_plural': 'Exam',
                'db_table': 'exam',
            },
        ),
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.TextField(verbose_name='Question Title')),
                ('question_type', models.CharField(choices=[('correct_choices', 'correct_choices'), ('correct_essay', 'correct_essay'), ('correct_fill_the_blank_order', 'correct_fill_the_blank_order')], max_length=50, verbose_name='Question Type')),
                ('points', models.IntegerField(verbose_name='Points')),
                ('company_id', models.TextField(verbose_name='Company ID')),
                ('user_creator_id', models.TextField(verbose_name='User ID')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='Updated Date')),
            ],
            options={
                'verbose_name_plural': 'Questions',
                'db_table': 'question',
            },
        ),
        migrations.CreateModel(
            name='QuestionOption',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('option', models.TextField(verbose_name='Option')),
                ('correct_answer', models.BooleanField(default=False)),
                ('correct_answer_text', models.TextField(blank=True, null=True, verbose_name='Option Text')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='Updated Date')),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='assessment_database.Question', verbose_name='Type')),
            ],
            options={
                'verbose_name_plural': 'Question Options',
                'db_table': 'question_option',
                'unique_together': {('question', 'option')},
            },
        ),
        migrations.CreateModel(
            name='ExamQuestion',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('order', models.IntegerField(default=1, verbose_name='Order')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='Updated Date')),
                ('exam', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='assessment_database.Exam', verbose_name='Type')),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='assessment_database.Question', verbose_name='Type')),
            ],
            options={
                'verbose_name_plural': 'Exam Question',
                'db_table': 'exam_question',
                'unique_together': {('exam', 'question')},
            },
        ),
        migrations.CreateModel(
            name='Answer',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('user_id', models.TextField(verbose_name='User')),
                ('app_id', models.TextField(null=True, verbose_name='Application')),
                ('enrollment_id', models.TextField(null=True, verbose_name='Enrollment')),
                ('options', models.TextField(verbose_name='Options')),
                ('is_ok', models.BooleanField(default=False, verbose_name='Is ok?')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='Updated Date')),
                ('question', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='assessment_database.Question', verbose_name='Exam Has Question')),
            ],
            options={
                'verbose_name_plural': 'Answers',
                'db_table': 'answer',
                'unique_together': {('question', 'user_id', 'enrollment_id')},
            },
        ),
    ]
