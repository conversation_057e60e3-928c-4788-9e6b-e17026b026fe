# -*- coding: utf-8 -*-
from rest_framework import status
from rest_framework.response import Response
from rest_framework import viewsets
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from assessment_database.models import Answer, ExamQuestion, Question, Exam
from assessment_database.serializers.answer_serializer import AnswerSerializer
from custom.keeps_exception_handler import KeepsBadRequestError, KeepsClientHeaderNotFoundCompanyError, \
    KeepsNotAllowedAccessCompanyError


class AnswerExamViewSet(viewsets.ModelViewSet):

    def __init__(self):
        pass

    def get_queryset(self):
        company_id = self.request.user.get('client_id')

        if not company_id:
            raise KeepsClientHeaderNotFoundCompanyError()

        exam = Exam.objects.filter(id=str(self.kwargs['exam_id']), company_id=company_id).first()

        if not exam:
            raise KeepsNotAllowedAccessCompanyError()

        questions = ExamQuestion.objects.filter(exam_id=str(self.kwargs['exam_id'])).values_list('question_id', flat=True)
        return Answer.objects.filter(question_id__in=questions)

    def get_serializer_class(self):
        return AnswerSerializer


class AnswerQuestionViewSet(viewsets.ModelViewSet):

    def __init__(self):
        pass

    def get_queryset(self):
        company_id = self.request.user.get('client_id')

        if not company_id:
            raise KeepsClientHeaderNotFoundCompanyError()

        question = Question.objects.filter(id=str(self.kwargs['question_id']), company_id=company_id)

        if not question:
            raise KeepsNotAllowedAccessCompanyError()

        return Answer.objects.filter(question_id=str(self.kwargs['question_id']))

    def get_serializer_class(self):
        return AnswerSerializer


class AnswerViewSet(viewsets.ModelViewSet):

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ('question', 'user_id', 'app_id', 'enrollment_id')
    search_fields = ('question', 'user_id', 'app_id', 'enrollment_id')
    ordering_fields = ('question', 'user_id', 'created_date', 'updated_date',)
    ordering = ('updated_date',)

    def __init__(self):
        pass

    def get_queryset(self):
        company_id = self.request.user.get('client_id')

        if not company_id:
            raise KeepsClientHeaderNotFoundCompanyError()

        return Answer.objects.filter()

    def get_serializer_class(self):
        return AnswerSerializer

    # pylint: disable-msg=too-many-locals
    def create(self, request, *args, **kwargs):

        answers_data = request.data
        answers = []
        app_id = None
        user_id = None
        enrollment_id = None

        if 'user_id' in answers_data:
            user_id = answers_data['user_id']
        elif request.user.get('sub'):
            user_id = request.user.get('sub')

        if 'app_id' in answers_data:  # noqa: SIM908
            app_id = answers_data['app_id']

        if 'enrollment_id' in answers_data:  # noqa: SIM908
            enrollment_id = answers_data['enrollment_id']

        for _answer in answers_data['questions']:
            try:
                question = Question.objects.get(id=_answer['id'])
            except Exception:
                # pylint: disable-msg=raise-missing-from
                raise KeepsBadRequestError(i18n='question_not_found', detail='Question not found, check if ID is correct')

            question_options = question.options.all()
            correct_options = [str(entry.id) for entry in question_options if entry.correct_answer is True]
            user_options = _answer['options']
            is_ok = set(correct_options) == set(user_options)

            answer = Answer()
            answer.user_id = user_id
            answer.app_id = app_id
            answer.enrollment_id = enrollment_id
            answer.options = ','.join(user_options)
            answer.question = question
            answer.is_ok = is_ok
            answer.save()
            answers.append(AnswerSerializer(answer).data)

        return Response(answers, status=status.HTTP_201_CREATED)
