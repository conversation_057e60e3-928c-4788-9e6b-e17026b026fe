from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.filters import <PERSON><PERSON><PERSON><PERSON>, OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from assessment_database.models import Question, ExamQuestion
from assessment_database.serializers.question_serializer import (
    QuestionAndOptionsSerializer,
    QuestionAndOptionsSaveSerializer,
)
from assessment_database.services.question_service import QuestionService
from authentication.keeps_with_client_authentication import KeepsWithClientAuthentication


class QuestionViewSet(viewsets.ModelViewSet):
    """
    A viewset for managing Questions
    """
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = ('title', 'company_id', 'user_creator_id')
    search_fields = ('title',)
    ordering_fields = ('title', 'created_date', 'updated_date')
    ordering = ('updated_date',)
    authentication_classes = (KeepsWithClientAuthentication,)

    def get_queryset(self):
        company_id = self.request.user.get('client_id')

        if 'exam_id' in self.kwargs:
            question_ids = ExamQuestion.objects.filter(
                exam_id=self.kwargs['exam_id']
            ).values_list('question_id', flat=True)
            return Question.objects.filter(company_id=company_id, id__in=question_ids)

        return Question.objects.filter(company_id=company_id)

    def get_serializer_class(self):
        if self.action in ['list', 'retrieve']:
            return QuestionAndOptionsSerializer
        return QuestionAndOptionsSaveSerializer

    def get_service(self):
        return QuestionService(user=self.request.user, company_id=self.request.user.get('client_id'))

    def create(self, request, *args, **kwargs):
        service = self.get_service()
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        options_data = serializer.validated_data.pop('options', None)

        question = service.create_question(
            validated_data=serializer.validated_data,
            options_data=options_data
        )

        if 'exam_id' in kwargs:
            service.link_to_exam(kwargs['exam_id'], question.id, request.data.get('order'))

        return Response(
            QuestionAndOptionsSerializer(question).data,
            status=status.HTTP_201_CREATED
        )

    def update(self, request, *args, **kwargs):
        service = self.get_service()
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)

        question = service.update_question(
            question=instance,
            validated_data=serializer.validated_data,
            options_data=serializer.validated_data.get('options', None)
        )

        return Response(
            QuestionAndOptionsSerializer(question).data
        )
