# -*- coding: utf-8 -*-
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets
from rest_framework.filters import <PERSON>Filter, OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.response import Response
from assessment_database.models import Exam
from assessment_database.serializers.exam_serializer import ExamSerializer, ExamDetailSerializer, ExamPostSerializer
from config.docs import SwaggerAutoSchemaCustom
from learn_content.models.content_type import ContentType


class ExamViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filter_fields = ('title', 'company_id', 'user_creator_id')
    search_fields = ('title', 'company_id', 'user_creator_id')
    ordering_fields = ('title', 'created_date', 'updated_date',)
    ordering = ('updated_date',)

    def __init__(self):
        pass

    def get_queryset(self):
        company_id = self.request.user.get('client_id')

        if not company_id:
            raise ValueError("Not allowed resource without client-id on request")

        return Exam.objects.filter(company_id=company_id)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        kwargs_serializer = {'context': self.get_serializer_context()}
        serializer = ExamDetailSerializer(instance, **kwargs_serializer)
        return Response(serializer.data)

    @swagger_auto_schema(
        auto_schema=SwaggerAutoSchemaCustom,
        request_body=ExamPostSerializer)
    def create(self, request, *args, **kwargs):
        """
        ---

        Create a new exam

        ---
            company_id => will be set by x-client in header
            user_creator_id => will be set with user logged id
            content_type => will be set as "Question" automatically.
        """
        content_type = ContentType.objects.filter(name='Question').get()
        request.data['content_type'] = str(content_type.id)
        request.data['company_id'] = self.request.user.get('client_id')
        request.data['user_creator_id'] = self.request.user.get('sub')
        # pylint: disable-msg=super-with-arguments
        response = super(ExamViewSet, self).create(request, *args, **kwargs)
        return response

    @swagger_auto_schema(
        responses={
            '200': ExamSerializer
        })
    def update(self, request, *args, **kwargs):
        """
        ---

        Update (patch or put) the exam

        ---
            company_id => will be set by x-client in header
            user_creator_id => will be set with user logged id
            content_type => will be set as "Question" automatically.
        """
        content_type = ContentType.objects.filter(name='Question').get()
        request.data['content_type'] = str(content_type.id)
        request.data['company_id'] = self.request.user.get('client_id')
        request.data['user_creator_id'] = self.request.user.get('sub')
        # pylint: disable-msg=super-with-arguments
        response = super(ExamViewSet, self).update(request, *args, **kwargs)
        return response

    def get_serializer_class(self):
        return ExamSerializer
