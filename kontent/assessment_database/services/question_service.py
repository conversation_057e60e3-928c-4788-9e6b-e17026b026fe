from typing import List, Union
from uuid import UUID

from django.db import transaction
from assessment_database.models import Question, QuestionOption, ExamQuestion
from custom.exceptions.kp_not_allowed_to_edit_question import KPNotAllowedToEditQuestion
from custom.exceptions.kp_unable_to_edit_question_already_answered import KPUnableToEditQuestionWithAnswers
from learn_content.models import ContentPointRule


class QuestionService:
    def __init__(self, user, company_id: str):
        self.user = user
        self.company_id = company_id
        content_rule = ContentPointRule.objects.filter(content_type__name='Question').first()
        self.points_default = content_rule.points if content_rule else 0

    @transaction.atomic
    def create_question(self, validated_data: dict, options_data: List[dict] = None):
        validated_data.update({
            'company_id': self.company_id,
            'user_creator_id': self.user.get('sub'),
            'points': self.points_default,
        })

        question = Question.objects.create(**validated_data)
        if options_data:
            self._create_options(question, options_data)
        question.options.all()
        return question

    def _already_answered(self, question: Question) -> bool:
        return question.answer_set.filter().exists()

    @transaction.atomic
    def update_question(self, question: Question, validated_data: dict, options_data: List[dict] = None):
        if self._already_answered(question):
            raise KPUnableToEditQuestionWithAnswers()
        if question.company_id != self.company_id:
            raise KPNotAllowedToEditQuestion()
        question.update(**validated_data)
        question.save()

        if options_data is not None:
            question.options.all().delete()
            self._create_options(question, options_data)

        return question

    def link_to_exam(self, exam_id: Union[str, UUID], question_id: str, order: int):
        ExamQuestion.objects.create(
            exam_id=exam_id,
            question_id=question_id,
            order=order
        )

    def _create_options(self, question: Question, options_data: List[dict]):
        options_to_create = []
        for option_data in options_data:
            options_to_create.append(QuestionOption(question=question, **option_data))
        QuestionOption.objects.bulk_create(options_to_create)
