# -*- coding: utf-8 -*-
import os
import shutil
import uuid
from uuid import uuid4
import mock
from django.test import <PERSON><PERSON><PERSON>
from analyze import Analy<PERSON>, AnalyzeScorm
from custom.keeps_exception_handler import KeepsBadRequestError


@mock.patch('utils.aws.AmazonS3.send_file', return_value={})
class AnalyzeScormTestCase(TestCase):

    def setUp(self):
        self._analyzer = Analyzer()
        self._base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self._workspace_id = uuid.uuid4()

    def test_process_scorm_multiple_contents(self, mock_s3_client):
        file_name = str(uuid4())
        scorm_file = f'{self._base_dir}/tests/files/sample_scorm.zip'
        sub_folder = 'scorm'
        temp_file = self.copy_file_temp(scorm_file)
        self._analyzer.set_analyze_command(AnalyzeScorm(
            file_path=temp_file,
            file_name=file_name,
            point_rules=5,
            point_quantity=1,
            duration=1200,
            workspace_id=self._workspace_id
        )
        )

        scorm = self._analyzer.process_content()

        mock_s3_client.assert_called_once_with(
            bucket='keeps.kontent.media.hml',
            file_name=f'{file_name}.zip',
            file_path=temp_file,
            sub_folder=sub_folder,
            workspace_id=self._workspace_id
        )

        self.assertFalse(os.path.exists(temp_file))

        course_title = scorm.get("course_title")
        self.assertEqual(course_title, 'Tivit')

        course_steps = scorm.get("course_steps")
        self.assertEqual(len(course_steps), 1)

        self.assertEqual(course_steps[0]['step_title'], 'Tivit')
        self.assertEqual(course_steps[0]['step_order'], 1)
        self.assertEqual(len(course_steps[0]['contents']), 10)

        contents = course_steps[0]['contents']
        self.assertEqual(contents[0]['url'], f'https://s3.amazonaws.com/keeps.kontent.media.hml/{sub_folder}/{file_name}/33-habilidades-digitais/sco-1.html')
        self.assertEqual(contents[0]['title'], 'O que vem por aí...')
        self.assertEqual(contents[0]['duration'], 120)
        self.assertEqual(contents[0]['points'], 10)
        self.assertEqual(contents[0]['description'], 'RES-EB4C149A6F3A3211598C6EB78017D83B')

    def test_process_scorm_single_contents(self, mock_s3_client):
        file_name = str(uuid4())
        scorm_file = f'{self._base_dir}/tests/files/sample_scorm_2.zip'
        sub_folder = 'scorm'
        temp_file = self.copy_file_temp(scorm_file)
        self._analyzer.set_analyze_command(AnalyzeScorm(
            file_path=temp_file,
            file_name=file_name,
            point_rules=5,
            point_quantity=1,
            duration=1200,
            workspace_id=self._workspace_id
        )
        )
        scorm = self._analyzer.process_content()
        mock_s3_client.assert_called_once_with(
            bucket='keeps.kontent.media.hml',
            file_name=f'{file_name}.zip',
            file_path=temp_file,
            sub_folder=sub_folder,
            workspace_id=self._workspace_id
        )

        self.assertFalse(os.path.exists(temp_file))

        course_title = scorm.get("course_title")
        self.assertEqual(course_title, 'Tivit')

        course_steps = scorm.get("course_steps")
        self.assertEqual(len(course_steps), 1)

        self.assertEqual(course_steps[0]['step_title'], 'Tivit')
        self.assertEqual(course_steps[0]['step_order'], 1)
        self.assertEqual(len(course_steps[0]['contents']), 1)

        contents = course_steps[0]['contents']
        self.assertEqual(contents[0]['url'], f'https://s3.amazonaws.com/keeps.kontent.media.hml/{sub_folder}/{file_name}/index.html')
        self.assertEqual(contents[0]['title'], 'Item')
        self.assertEqual(contents[0]['duration'], 1200)
        self.assertEqual(contents[0]['points'], 100)
        self.assertEqual(contents[0]['description'], 'RES-DB9E66E13EE3231F3BFF04B47FC8715D')

    def test_process_scorm_without_imanifest_xml(self, mock_s3_client):
        file_name = str(uuid4())
        scorm_file = f'{self._base_dir}/tests/files/sample_scorm_error.zip'
        temp_file = self.copy_file_temp(scorm_file)
        self._analyzer.set_analyze_command(AnalyzeScorm(
            file_path=temp_file,
            file_name=file_name,
            duration=1000)
        )
        with self.assertRaises(KeepsBadRequestError) as error:
            self._analyzer.process_content()

        self.assertEqual(error.exception.i18n, 'imanifest_file_not_found')
        self.assertEqual(error.exception.detail, 'Scorm package should contain Imanifest.xml file')
        self.assertEqual(error.exception.status_code, 400)

    def copy_file_temp(self, file_to_copy):
        extension = file_to_copy.split('/')[-1].split('.')[1]
        new_file = f'{self._base_dir}/tests/files/temp-process.{extension}'
        shutil.copyfile(file_to_copy, new_file)
        return new_file
