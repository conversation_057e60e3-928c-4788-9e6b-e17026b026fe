language_aws_comprehend = {
    'Languages': [
        {'LanguageCode': 'pt',
         'Score': 0.9990849494934082
         }
    ]}

entity_aws_comprehend = [
    {
        'Score': 0.7804453372955322,
        'Type': 'COMMERCIAL_ITEM',
        'Text': 'LXP', 'BeginOffset': 2, 'EndOffset': 5
    },
    {'Score': 0.7874088883399963,
     'Type': 'ORGANIZATION',
     'Text': 'Keeps',
     'BeginOffset': 9,
     'EndOffset': 14
     }
]

keys_aws_comprehend = [
    {'Score': 0.9991660118103027,
     'Text': 'A LXP da Keeps',
     'BeginOffset': 0, 'EndOffset': 14},
    {'Score': 0.9997682571411133,
     'Text': 'uma solução simples e completa de capacitação com inteligência artificial',
     'BeginOffset': 17, 'EndOffset': 90
     },
    {'Score': 0.9998513460159302,
     'Text': 'mobilidade',
     'BeginOffset': 92, 'EndOffset': 102
     },
    {'Score': 0.9997623562812805,
     'Text': 'gamificação',
     'BeginOffset': 104, 'EndOffset': 115
     },
    {'Score': 0.9997573494911194,
     'Text': 'design intuitivo',
     'BeginOffset': 118, 'EndOffset': 134
     }]

transcript = 'A LXP da Keeps é uma solução simples e completa de capacitação com inteligência artificial, mobilidade, gamificação e design intuitivo.'
transcript_ppt = 'A LXP da Keeps é uma solução simples e completa de capacitação com inteligência artificial, mobilidade, gamificação e design intuitivo.Página 2 do PPT para testes'


youtube_dl_info_no_caption_no_tags = {
    'description': "Description",
    'duration': 389,
    'tags': [],
    'automatic_captions': {}
}


youtube_dl_info_with_caption_with_tags = {
    'description': "Description",
    'duration': 389,
    'tags': ["tag1",  "tag2"],
    'automatic_captions': {
        'es': [
            {
                'ext': 'vtt',
                'url': 'https://www.youtube.com/api/timedtext?v=xEnu8FJ8Tz4&asr_langs=de%2Cen%2Ces%2Cfr%2Cid%2Cit%2Cja%2Cko%2Cnl%2Cpt%2Cru%2Ctr%2Cvi&caps=asr&exp=xftt%2Cxctw&xoaf=5&hl=en&ip=0.0.0.0&ipbits=0&expire=1631678679&sparams=ip%2Cipbits%2Cexpire%2Cv%2Casr_langs%2Ccaps%2Cexp%2Cxoaf&signature=7DA65A257C67C9E4EDC10D689EE70C3F45EC3F84.253034C1CB09DEF2133449E2338090FD39C43EDC&key=yt8&kind=asr&lang=pt&tlang=es&fmt=vtt'
            }
        ],
        'pt': [
            {
                'ext': 'vtt',
                'url': 'https://www.youtube.com/api/timedtext?v=xEnu8FJ8Tz4&asr_langs=de%2Cen%2Ces%2Cfr%2Cid%2Cit%2Cja%2Cko%2Cnl%2Cpt%2Cru%2Ctr%2Cvi&caps=asr&exp=xftt%2Cxctw&xoaf=5&hl=en&ip=0.0.0.0&ipbits=0&expire=1631678679&sparams=ip%2Cipbits%2Cexpire%2Cv%2Casr_langs%2Ccaps%2Cexp%2Cxoaf&signature=7DA65A257C67C9E4EDC10D689EE70C3F45EC3F84.253034C1CB09DEF2133449E2338090FD39C43EDC&key=yt8&kind=asr&lang=pt&tlang=pt&fmt=vtt'
            }
        ]
    }
}

youtube_dl_info_no_caption_with_tags = {
    'description': "Description",
    'duration': 389,
    'tags': ["tag1", "tag2"],
    'automatic_captions': {}
}

youtube_dl_info_with_caption_no_tags = {
    'description': "Description",
    'duration': 389,
    'automatic_captions': {
        'es': [
            {
                'ext': 'vtt',
                'url': 'https://www.youtube.com/api/timedtext?v=xEnu8FJ8Tz4&asr_langs=de%2Cen%2Ces%2Cfr%2Cid%2Cit%2Cja%2Cko%2Cnl%2Cpt%2Cru%2Ctr%2Cvi&caps=asr&exp=xftt%2Cxctw&xoaf=5&hl=en&ip=0.0.0.0&ipbits=0&expire=1631678679&sparams=ip%2Cipbits%2Cexpire%2Cv%2Casr_langs%2Ccaps%2Cexp%2Cxoaf&signature=7DA65A257C67C9E4EDC10D689EE70C3F45EC3F84.253034C1CB09DEF2133449E2338090FD39C43EDC&key=yt8&kind=asr&lang=pt&tlang=es&fmt=vtt'
            }
        ],
        'pt': [
            {
                'ext': 'vtt',
                'url': 'https://www.youtube.com/api/timedtext?v=xEnu8FJ8Tz4&asr_langs=de%2Cen%2Ces%2Cfr%2Cid%2Cit%2Cja%2Cko%2Cnl%2Cpt%2Cru%2Ctr%2Cvi&caps=asr&exp=xftt%2Cxctw&xoaf=5&hl=en&ip=0.0.0.0&ipbits=0&expire=1631678679&sparams=ip%2Cipbits%2Cexpire%2Cv%2Casr_langs%2Ccaps%2Cexp%2Cxoaf&signature=7DA65A257C67C9E4EDC10D689EE70C3F45EC3F84.253034C1CB09DEF2133449E2338090FD39C43EDC&key=yt8&kind=asr&lang=pt&tlang=pt&fmt=vtt'
            }
        ]
    }
}