# -*- coding: utf-8 -*-
import os
import shutil
from uuid import uuid4
import mock
from django.test import Test<PERSON>ase
from analyze import Analy<PERSON>, AnalyzePD<PERSON>
from analyze.tests.mock_fixtures import language_aws_comprehend, entity_aws_comprehend, keys_aws_comprehend, transcript


@mock.patch('utils.aws.AmazonS3.send_file', return_value={"url": "amazon"})
class AnalyzePDFTestCase(TestCase):

    def setUp(self):
        self._analyzer = Analyzer()
        self._base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    def test_process_pdf(self, mock_s3):
        file_name = str(uuid4())
        pdf_file = f'{self._base_dir}/tests/files/sample.pdf'
        temp_file = self.copy_file_temp(pdf_file)

        self._analyzer.set_analyze_command(AnalyzePDF(
            file_name=f"{file_name}.pdf",
            points_rules=10,
            points_quantity=1,
            file_path=temp_file)
        )
        pdf = self._analyzer.process_content()
        mock_s3.assert_called_once_with(
            file_name=f'{file_name}.pdf',
            file_path=temp_file,
        )

        self.assertFalse(os.path.exists(temp_file))
        self.assertEqual(pdf.get("url"), "amazon")
        self.assertEqual(pdf.get("analyzed"), False)

    def copy_file_temp(self, file_to_copy):
        extension = file_to_copy.split('/')[-1].split('.')[1]
        new_file = f'{self._base_dir}/tests/files/temp-process.{extension}'
        shutil.copyfile(file_to_copy, new_file)
        return new_file
