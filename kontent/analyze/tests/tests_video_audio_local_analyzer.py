# -*- coding: utf-8 -*-
import os
import shutil
from uuid import uuid4
import mock
from django.test import TestCase
from analyze import Ana<PERSON><PERSON>, AnalyzeVideoAudio

transcribe_job = {
    "TranscriptionJob": {
        "TranscriptionJobName": "5d4959f1-5c51-4f2c-a6ee-31854dfc5a5d",
        "TranscriptionJobStatus": "IN_PROGRESS"
    }
}


@mock.patch('utils.aws.AmazonS3.send_file', return_value={"url": "amazon"})
@mock.patch('utils.aws.aws_transcribe.AmazonTranscribe.transcribe_async', return_value=transcribe_job)
class AnalyzeVideoAudioLocalTestCase(TestCase):

    def setUp(self):
        self._analyzer = Analyzer()
        self._base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    def test_process_mp3(self, mock_transcribe, mock_s3):
        file_name = str(uuid4())
        mp3_file = f'{self._base_dir}/tests/files/sample.mp3'
        temp_file = self.copy_file_temp(mp3_file)

        self._analyzer.set_analyze_command(AnalyzeVideoAudio(
            file_name=f"{file_name}.mp3",
            points_rules=10,
            points_quantity=1,
            file_path=temp_file)
        )
        mp3 = self._analyzer.process_content()
        mock_s3.assert_called_once_with(
            file_name=f'{file_name}.mp3',
            file_path=temp_file,
            workspace_id=''
        )

        # mock_transcribe.assert_called_once_with(file_url='amazon')

        self.assertFalse(os.path.exists(temp_file))
        self.assertEqual(mp3.get("duration"), 18)
        self.assertEqual(mp3.get("points"), 10)
        self.assertEqual(mp3.get("url"), "amazon")
        self.assertEqual(mp3.get("analyzed"), False)

    def copy_file_temp(self, file_to_copy):
        extension = file_to_copy.split('/')[-1].split('.')[1]
        new_file = f'{self._base_dir}/tests/files/temp-process.{extension}'
        shutil.copyfile(file_to_copy, new_file)
        return new_file
