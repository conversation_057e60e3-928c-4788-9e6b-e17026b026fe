from analyze.parses.external_source import download_audio
from django.test import TestCase
import mock


class TestExternalSource(TestCase):
    @mock.patch('analyze.parses.external_source.yt_dlp.YoutubeDL')
    def test_download_audio(self, mock_ydl):
        url = 'https://www.youtube.com/watch?v=Eec0Tp4j5nA&t=23s'
        patch = download_audio(url)
        patch_call = patch[:-4]
        breakpoint()
        mock_ydl.assert_called_once_with({
            'format': 'bestaudio/best',
            'outtmpl': patch_call,
            'postprocessors': [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': 'mp3',
                'preferredquality': '192',
            }],
        })
        self.assertTrue(patch.endswith('.mp3'))
        self.assertFalse(patch.endswith('.mp3.mp3'))