# -*- coding: utf-8 -*-
import os
import shutil
from uuid import uuid4
import mock
from django.test import TestCase
from analyze import Analyzer, AnalyzeMsOffice
from analyze.tests.mock_fixtures import language_aws_comprehend, entity_aws_comprehend, \
    keys_aws_comprehend, transcript, transcript_ppt


@mock.patch('utils.google.gdrive.GoogleDrive.send_file', return_value={"url": "google_drive_url"})
@mock.patch('utils.aws.aws_comprehend.AwsComprehendClient.dominant_language', return_value=language_aws_comprehend)
@mock.patch('utils.aws.aws_comprehend.AwsComprehendClient.entities', return_value=entity_aws_comprehend)
@mock.patch('utils.aws.aws_comprehend.AwsComprehendClient.key_phrases', return_value=keys_aws_comprehend)
class AnalyzeMsOfficeTestCase(TestCase):

    def setUp(self):
        self._analyzer = Analyzer()
        self._base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    def test_process_docx(self, mock_keys, mock_entities, mock_language, mock_gdrive):
        file_name = str(uuid4())
        docx_file = f'{self._base_dir}/tests/files/sample.docx'
        temp_file = self.copy_file_temp(docx_file)
        self._analyzer.set_analyze_command(AnalyzeMsOffice(
            file_name=f"{file_name}.docx",
            points_rules=10,
            points_quantity=1,
            file_path=temp_file)
        )
        docx = self._analyzer.process_content()
        mock_gdrive.assert_called_once_with(
            file=temp_file,
            name=f'{file_name}.docx'
        )

        mock_language.assert_called_once()
        mock_keys.assert_called_once()
        mock_entities.assert_called_once()

        self.assertFalse(os.path.exists(temp_file))
        self.assertEqual(docx.get("duration"), 2)
        self.assertEqual(docx.get("points"), 1350)
        self.assertEqual(docx.get("transcript"), transcript)
        self.assertEqual(docx.get("language"), 'pt')
        self.assertEqual(docx.get("summary"), transcript)
        self.assertEqual(docx.get("url"), "google_drive_url")
        self.assertEqual(len(docx.get("entities")), 0)
        self.assertEqual(len(docx.get("tags")), 5)
        self.assertEqual(docx.get("analyzed"), True)

    def test_process_large_docx(self, mock_keys, mock_entities, mock_language, mock_gdrive):
        file_name = str(uuid4())
        docx_file = f'{self._base_dir}/tests/files/sample_large.docx'
        temp_file = self.copy_file_temp(docx_file)
        self._analyzer.set_analyze_command(AnalyzeMsOffice(
            file_name=f"{file_name}.docx",
            points_rules=10,
            points_quantity=1,
            file_path=temp_file)
        )
        docx = self._analyzer.process_content()
        mock_gdrive.assert_called_once_with(
            file=temp_file,
            name=f'{file_name}.docx'
        )

        mock_language.assert_called_once()
        mock_keys.assert_called_once()
        mock_entities.assert_called_once()

        self.assertFalse(os.path.exists(temp_file))
        self.assertEqual(docx.get("duration"), 244)
        self.assertEqual(docx.get("points"), 141200)
        self.assertEqual(len(docx.get("transcript")), 14120)
        self.assertEqual(docx.get("language"), 'pt')
        self.assertEqual(docx.get("url"), "google_drive_url")
        self.assertEqual(len(docx.get("entities")), 0)
        self.assertEqual(len(docx.get("tags")), 5)
        self.assertEqual(docx.get("analyzed"), True)

    def test_process_pptx(self, mock_keys, mock_entities, mock_language, mock_gdrive):
        file_name = str(uuid4())
        pptx_file = f'{self._base_dir}/tests/files/sample.pptx'
        temp_file = self.copy_file_temp(pptx_file)
        self._analyzer.set_analyze_command(AnalyzeMsOffice(
            file_name=f"{file_name}.pptx",
            points_rules=10,
            points_quantity=1,
            file_path=temp_file)
        )
        pptx = self._analyzer.process_content()
        mock_gdrive.assert_called_once_with(
            file=temp_file,
            name=f'{file_name}.pptx'
        )

        mock_language.assert_called_once()
        mock_keys.assert_called_once()
        mock_entities.assert_called_once()

        self.assertFalse(os.path.exists(temp_file))
        self.assertEqual(pptx.get("duration"), 23)
        self.assertEqual(pptx.get("points"), 20)
        self.assertEqual(pptx.get("transcript"), transcript_ppt)
        self.assertEqual(pptx.get("language"), 'pt')
        self.assertEqual(pptx.get("summary"), transcript_ppt)
        self.assertEqual(pptx.get("url"), "google_drive_url")
        self.assertEqual(len(pptx.get("entities")), 0)
        self.assertEqual(len(pptx.get("tags")), 5)
        self.assertEqual(pptx.get("analyzed"), True)

    def test_process_xlsx(self, mock_keys, mock_entities, mock_language, mock_gdrive):
        file_name = str(uuid4())
        xlsx_file = f'{self._base_dir}/tests/files/sample.xlsx'
        temp_file = self.copy_file_temp(xlsx_file)
        self._analyzer.set_analyze_command(AnalyzeMsOffice(
            file_name=f"{file_name}.xlsx",
            points_rules=5,
            points_quantity=100,
            file_path=temp_file)
        )
        xlsx = self._analyzer.process_content()
        mock_gdrive.assert_called_once_with(
            file=temp_file,
            name=f'{file_name}.xlsx'
        )

        mock_language.assert_called_once()
        mock_keys.assert_called_once()
        mock_entities.assert_called_once()

        self.assertFalse(os.path.exists(temp_file))
        self.assertEqual(xlsx.get("duration"), 2)
        self.assertEqual(xlsx.get("points"), 6)
        self.assertEqual(xlsx.get("transcript").replace('\n', ""), transcript)
        self.assertEqual(xlsx.get("language"), 'pt')
        self.assertEqual(xlsx.get("summary").replace('\n', ""), transcript)
        self.assertEqual(xlsx.get("url"), "google_drive_url")
        self.assertEqual(len(xlsx.get("entities")), 0)
        self.assertEqual(len(xlsx.get("tags")), 5)
        self.assertEqual(xlsx.get("analyzed"), True)

    def copy_file_temp(self, file_to_copy):
        extension = file_to_copy.split('/')[-1].split('.')[1]
        new_file = f'{self._base_dir}/tests/files/temp-process.{extension}'
        shutil.copyfile(file_to_copy, new_file)
        return new_file
