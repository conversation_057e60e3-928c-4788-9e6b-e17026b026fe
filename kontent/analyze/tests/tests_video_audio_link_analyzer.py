# -*- coding: utf-8 -*-
import os
import mock
from django.test import TestCase
from analyze import Analy<PERSON>, AnalyzeVideoAudioLink
from custom.keeps_exception_handler import KeepsBadRequestError
from . import mock_fixtures as fixtures


class AnalyzeVideoAudioLinkTestCase(TestCase):

    def setUp(self):
        self._analyzer = Analyzer()
        self._base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    def test_process_youtube_link_not_available(self):
        youtube_unavailable = "https://www.youtube.com/watch?v=eTkRHXJsmTM"
        self._analyzer.set_analyze_command(AnalyzeVideoAudioLink(
            points_rules=10,
            points_quantity=1,
            url=youtube_unavailable)
        )
        with self.assertRaises(KeepsBadRequestError) as error:
            self._analyzer.process_content()

        self.assertEqual(error.exception.i18n, 'video_unavailable')
        self.assertEqual(error.exception.detail, 'Check if content is available')
        self.assertEqual(error.exception.status_code, 400)

    def test_process_youtube_link_not_public(self):
        youtube_unavailable = "https://www.youtube.com/watch?v=sEtzsCISEx8"
        self._analyzer.set_analyze_command(AnalyzeVideoAudioLink(
            points_rules=10,
            points_quantity=1,
            url=youtube_unavailable)
        )
        with self.assertRaises(KeepsBadRequestError) as error:
            self._analyzer.process_content()

        self.assertEqual(error.exception.i18n, 'private_video')
        self.assertEqual(error.exception.detail, 'Video content should be public')
        self.assertEqual(error.exception.status_code, 400)

    @mock.patch("yt_dlp.YoutubeDL.extract_info", return_value=fixtures.youtube_dl_info_no_caption_no_tags)
    def test_download_link_info_no_captions_no_tags(self, mock):
        youtube_link = "https://www.youtube.com/watch?v=sEtzsCISEx8"
        self._analyzer.set_analyze_command(AnalyzeVideoAudioLink(
            points_rules=10,
            points_quantity=1,
            url=youtube_link)
        )
        response = self._analyzer.process_content()
        self.assertEqual(response.get("url"), youtube_link)
        self.assertEqual(response.get("duration"), 389)
        self.assertEqual(response.get("points"), 70)
        self.assertEqual(response.get("tags"), None)
        self.assertEqual(response.get("summary"), "Description")
        self.assertEqual(response.get("transcript"), None)
        self.assertEqual(response.get("language"), None)
        self.assertEqual(response.get("analyzed"), False)

    def test_download_link_info_with_captions_with_tags(self):
        youtube_link = "https://www.youtube.com/watch?v=xEnu8FJ8Tz4"
        self._analyzer.set_analyze_command(AnalyzeVideoAudioLink(
            points_rules=10,
            points_quantity=1,
            url=youtube_link)
        )
        response = self._analyzer.process_content()
        self.assertEqual(response.get("url"), youtube_link)
        self.assertEqual(response.get("duration"), 119)
        self.assertEqual(response.get("points"), 20)
        self.assertEqual(response.get("tags"), [{'tag': 'Keeps', 'relevance': 1}, {'tag': 'LXP', 'relevance': 1}, {'tag': 'SmartZap', 'relevance': 1}])
        self.assertEqual(str(response.get("summary")).__contains__("Esse pequeno vídeo vai ajudar"), True)
        self.assertEqual(str(response.get("transcript")).__contains__("00:00 Kind: captions Language: pt o olá bem-vindos ao smart zap"), True)
        self.assertEqual(response.get("language"), None)
        self.assertEqual(response.get("analyzed"), True)

    @mock.patch("yt_dlp.YoutubeDL.extract_info", return_value=fixtures.youtube_dl_info_no_caption_with_tags)
    def test_download_link_info_no_captions_with_tags(self, mock):
        youtube_link = "https://www.youtube.com/watch?v=sEtzsCISEx8"
        self._analyzer.set_analyze_command(AnalyzeVideoAudioLink(
            points_rules=10,
            points_quantity=1,
            url=youtube_link)
        )
        response = self._analyzer.process_content()
        self.assertEqual(response.get("url"), youtube_link)
        self.assertEqual(response.get("duration"), 389)
        self.assertEqual(response.get("points"), 70)
        self.assertEqual(response.get("tags"), [{'tag': 'tag1', 'relevance': 1}, {'tag': 'tag2', 'relevance': 1}])
        self.assertEqual(response.get("summary"), "Description")
        self.assertEqual(response.get("transcript"), None)
        self.assertEqual(response.get("language"), None)
        self.assertEqual(response.get("analyzed"), True)

    def test_download_link_info_with_captions_no_tags(self):
        youtube_link = "https://www.youtube.com/watch?v=7-QqAbhaVJ0"
        self._analyzer.set_analyze_command(AnalyzeVideoAudioLink(
            points_rules=10,
            points_quantity=1,
            url=youtube_link)
        )
        response = self._analyzer.process_content()

        print(len(response.get("tags")))
        self.assertEqual(response.get("url"), youtube_link)
        self.assertEqual(response.get("duration"), 209)
        self.assertEqual(response.get("points"), 40)
        self.assertEqual(len(response.get("tags")), 46)
        self.assertEqual(str(response.get("summary")).__contains__("uma plataforma de experiências de aprendizagem"), False)
        self.assertEqual(str(response.get("transcript")).__contains__("uma plataforma de experiências de aprendizagem"), False)
        self.assertEqual(response.get("language"), None)
        self.assertEqual(response.get("analyzed"), True)