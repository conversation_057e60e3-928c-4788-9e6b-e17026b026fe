import mock
from django.test import Test<PERSON>ase
import os

from mock.mock import <PERSON><PERSON>ock

from analyze import <PERSON><PERSON><PERSON>, YoutubeVideoAnalyzer
from config.settings import BASE_DIR


class MockedYoutube:
    def __init__(self, url: str):
        self.url = url

    @property
    def description(self):
        return "description"

    @property
    def length(self):
        return 360

    @property
    def vid_info(self):
        return {'playabilityStatus': {'status': 'SUCCESS'}, 'videoDetails': {'keywords': ['test', 'test2']}}


class AnalyzeVideoAudioLinkTestCase(TestCase):

    def setUp(self):
        self._analyzer = Analyzer()
        self._base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    @mock.patch.object(YoutubeVideoAnalyzer, '_load_youtube_video')
    def test_process_youtube_link(self, load_youtube_video: mock.MagicMock):
        url = "https://www.youtube.com/watch?v=ICBaHo2Uv5I&ab_channel=KeepsLearn"
        load_youtube_video.return_value = MockedYoutube(url)

        analyzer = YoutubeVideoAnalyzer(container=MagicMock(), points_rules=10, points_quantity=1, url=url)

        result = analyzer.execute()

        load_youtube_video.assert_called()
        self.assertEqual(
            result,
            {
                'analyzed': True,
                'duration': 360,
                'language': None,
                'points': 60,
                'summary': 'description',
                'tags': [{'relevance': 1, 'tag': 'test'}, {'relevance': 1, 'tag': 'test2'}],
                'transcript': None,
                'url': url
             }
        )
