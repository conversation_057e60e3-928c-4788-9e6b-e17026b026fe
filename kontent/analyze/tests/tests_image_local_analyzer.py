# -*- coding: utf-8 -*-
import os
import shutil
from uuid import uuid4
import mock
from django.test import TestCase
from analyze import Analyzer, AnalyzeImage
from analyze.tests.mock_fixtures import language_aws_comprehend, entity_aws_comprehend, keys_aws_comprehend, transcript


@mock.patch('utils.aws.AmazonS3.send_file', return_value={"url": "amazon"})
@mock.patch('utils.aws.aws_comprehend.AwsComprehendClient.dominant_language', return_value=language_aws_comprehend)
@mock.patch('utils.aws.aws_comprehend.AwsComprehendClient.entities', return_value=entity_aws_comprehend)
@mock.patch('utils.aws.aws_comprehend.AwsComprehendClient.key_phrases', return_value=keys_aws_comprehend)
@mock.patch('utils.aws.aws_rekognition.AwsRekognitionClient.detect_image_text', return_value=transcript)
class AnalyzeImageTestCase(TestCase):

    def setUp(self):
        self._analyzer = Analyzer()
        self._base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    def test_process_png(self, mock_rekognition, mock_keys, mock_entities, mock_language, mock_s3):
        file_name = str(uuid4())
        image_file = f'{self._base_dir}/tests/files/sample.png'
        temp_file = self.copy_file_temp(image_file)

        self._analyzer.set_analyze_command(AnalyzeImage(
            file_name=f"{file_name}.png",
            points_rules=1,
            points_quantity=1,
            file_path=temp_file)
        )
        png = self._analyzer.process_content()
        mock_s3.assert_called_once_with(
            file_name=f'{file_name}.png',
            file_path=temp_file)

        mock_language.assert_called_once()
        mock_keys.assert_called_once()
        mock_entities.assert_called_once()
        mock_rekognition.assert_called_once()

        self.assertFalse(os.path.exists(temp_file))
        self.assertEqual(png.get("duration"), 2)
        self.assertEqual(png.get("points"), 2)
        self.assertEqual(png.get("transcript"), transcript)
        self.assertEqual(png.get("language"), 'pt')
        self.assertEqual(png.get("transcript"), transcript)
        self.assertEqual(png.get("url"), "amazon")
        self.assertEqual(png.get("analyzed"), True)

    def copy_file_temp(self, file_to_copy):
        extension = file_to_copy.split('/')[-1].split('.')[1]
        new_file = f'{self._base_dir}/tests/files/temp-process.{extension}'
        shutil.copyfile(file_to_copy, new_file)
        return new_file
