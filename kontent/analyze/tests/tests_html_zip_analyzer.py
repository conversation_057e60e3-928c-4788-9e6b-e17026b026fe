# -*- coding: utf-8 -*-
import os
import shutil
from uuid import uuid4
import mock
from config.settings import AWS_BUCKET_NAME, AWS_BASE_S3_URL
from django.test import TestCase
from analyze import Analyzer, AnalyzeHtmlFiles


@mock.patch('utils.aws.AmazonS3.send_file', return_value={"url": "amazon"})
class AnalyzeHtmlTestCase(TestCase):

    def setUp(self):
        self._analyzer = Analyzer()
        self._base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    def test_process_html_file_zip(self, mock_s3):
        provider = 'genially.html'
        file_name = str(uuid4())
        html_zip_file = f'{self._base_dir}/tests/files/sample_genially.zip'
        temp_file = self.copy_file_temp(html_zip_file)
        self._analyzer.set_analyze_command(AnalyzeHtmlFiles(
            file_path=temp_file,
            file_name=f"{file_name}",
            index_file=None,
            duration=1000,
            points_rules=10,
            points_quantity=1)
        )
        html = self._analyzer.process_content()
        mock_s3.assert_called()

        self.assertFalse(os.path.exists(temp_file))
        self.assertEqual(html.get("duration"), 1000)
        self.assertEqual(html.get("points"), 10000)
        self.assertEqual(html.get("analyzed"), True)
        self.assertEqual(html.get("url"), f"{AWS_BASE_S3_URL}/{AWS_BUCKET_NAME}/html/{file_name}/{provider}")

    def copy_file_temp(self, file_to_copy):
        extension = file_to_copy.split('/')[-1].split('.')[1]
        new_file = f'{self._base_dir}/tests/files/temp-process.{extension}'
        shutil.copyfile(file_to_copy, new_file)
        return new_file
