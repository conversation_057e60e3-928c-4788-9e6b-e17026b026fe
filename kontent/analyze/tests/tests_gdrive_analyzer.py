# -*- coding: utf-8 -*-
import os
import mock
from django.test import TestCase
from analyze import Analy<PERSON>, AnalyzeGdrive


@mock.patch('utils.aws.AmazonS3.send_file', return_value={})
class AnalyzeGdriveTestCase(TestCase):

    def setUp(self):
        self._analyzer = Analyzer()
        self._base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    def test_process_scorm_multiple_contents(self, mock_s3_client):
        gdrive_doc_url = 'https://docs.google.com/document/d/1LDoJMRpBTzqcaQ1b79Cx9cOHM6do2WlD/edit'
        self._analyzer.set_analyze_command(
            AnalyzeGdrive(points_rules=10, points_quantity=1, url=gdrive_doc_url)
        )
        gdrive = self._analyzer.process_content()
        self.assertEqual(gdrive.get('url'), 'https://docs.google.com/document/d/1LDoJMRpBTzqcaQ1b79Cx9cOHM6do2WlD')
        self.assertEqual(gdrive.get('analyzed'), False)