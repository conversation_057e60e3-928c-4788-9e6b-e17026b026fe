# pylint: disable=cyclic-import
import zipfile
import os
import re

import xmltodict
import chardet
from django_injector import inject

from di import Container
from custom.keeps_exception_handler import KeepsBadRequestError
from analyze import AnalyzeContent


# pylint: disable=no-value-for-parameter
# pylint: disable=unidiomatic-typecheck
# pylint: disable=no-value-for-parameter
# pylint: disable=consider-using-with
# pylint: disable=too-many-locals
class AnalyzeScorm(AnalyzeContent):

    @inject
    def __init__(
        self,
        container: Container,
        file_path: str,
        file_name: str,
        duration: int,
        points_rules: int,
        points_quantity: int,
        workspace_id: str = None
    ) -> None:
        super().__init__()
        self._file_to_analyze = file_path
        self._file_name = file_name
        self._duration = duration
        self._uploader = container.aws_s3_client()
        self._webhook_logger = container.webhook_logger
        self._workspace_id = workspace_id
        self._points_rules = points_rules
        self._points_quantity = points_quantity

    def execute(self) -> dict:
        """
        :return dict(upload, duration, points, transcribe_job):
        """
        try:
            ziped_file = zipfile.ZipFile(self._file_to_analyze)
            manifest_xml = [s for s in ziped_file.namelist() if "imsmanifest.xml" in s][0]
        except Exception as e:
            os.remove(self._file_to_analyze)
            raise KeepsBadRequestError(i18n='imanifest_file_not_found',
                                       detail='Scorm package should contain Imanifest.xml file') from e
        try:
            self._uploader.send_file(file_name=f'{self._file_name}.zip',
                                     file_path=self._file_to_analyze, bucket=self._uploader.bucket_name,
                                     sub_folder='scorm', workspace_id=self._workspace_id)

            course_title, scorm_steps = self.imanisfest_parse(
                self._file_to_analyze, manifest_xml, self._file_name, self._duration
            )
            os.remove(self._file_to_analyze)

            return {
                "course_title": course_title,
                "course_steps": scorm_steps
            }

        except Exception as error:
            self.logger("Failure to process SCORM package", str(error))
            raise KeepsBadRequestError(i18n='error_to_parse_xml_or_upload_scorm', detail=str(error)) from error

    def _decode_xml_content(self, xml_bytes):
        """
        Decode XML content with proper encoding detection.

        Args:
            xml_bytes (bytes): Raw XML content as bytes

        Returns:
            str: Decoded XML content

        Raises:
            UnicodeDecodeError: If all encoding attempts fail
        """
        # First, try to detect encoding from XML declaration
        xml_declaration_match = re.match(rb'<\?xml[^>]*encoding=["\']([^"\']+)["\']', xml_bytes)
        if xml_declaration_match:
            declared_encoding = xml_declaration_match.group(1).decode('ascii')
            try:
                return xml_bytes.decode(declared_encoding)
            except (UnicodeDecodeError, LookupError):
                # If declared encoding fails, continue with other methods
                pass

        # Try to detect encoding using chardet
        detected = chardet.detect(xml_bytes)
        if detected and detected['encoding'] and detected['confidence'] > 0.7:
            try:
                return xml_bytes.decode(detected['encoding'])
            except (UnicodeDecodeError, LookupError):
                # If detected encoding fails, continue with fallbacks
                pass

        # Fallback to common encodings
        common_encodings = ['utf-8', 'iso-8859-1', 'windows-1252', 'cp1252', 'latin1']
        for encoding in common_encodings:
            try:
                return xml_bytes.decode(encoding)
            except UnicodeDecodeError:
                continue

        # If all else fails, decode with errors='replace' to avoid complete failure
        return xml_bytes.decode('utf-8', errors='replace')

    def imanisfest_parse(self, zip_file, manifest_xml, package, duration):
        ziped_file = zipfile.ZipFile(zip_file)
        zip_file_path = manifest_xml.split('/')
        full_path = package

        if zip_file_path[-1] == 'imsmanifest.xml':
            zip_file_path.pop(-1)
            full_path = f'{package}{"/"}{"/".join(zip_file_path)}' if len(zip_file_path) > 0 else package

        # Read the XML content as bytes and decode with proper encoding detection
        xml_bytes = ziped_file.read(manifest_xml)
        xml_content = self._decode_xml_content(xml_bytes)
        imsmanifest_parsed = xmltodict.parse(xml_content)
        organization = imsmanifest_parsed['manifest']['organizations'].get('organization')
        steps = organization.get('item')
        activities_contents = imsmanifest_parsed['manifest']['resources'].get('resource')

        if not isinstance(steps, list):
            steps = [steps]
        

        course_title = organization.get('title') or "Course Title"
        scorm_steps = []

        duration = round(duration / len(steps))

        for i, step in enumerate(steps):
            _step = {
                "step_title": step.get("title") or f"Course Step {i + 1}",
                "step_order": i + 1,
                "contents": []
            }

            activities = step.get("item", None)

            if activities:
                duration_activity = round(duration / len(activities))
                points = self.compute_points_by_duration(duration_activity)
                for activity in activities:
                    result = self._scorm_activities(activities_contents, activity, full_path)
                    _step["contents"].append({"url": result.get('url'),
                                              "title": result.get('title'),
                                              "duration": duration_activity,
                                              "points": points,
                                              "description": result.get('description')})
            else:
                points = self.compute_points_by_duration(duration)
                result = self._scorm_activities(activities_contents, step, full_path)
                _step["contents"].append({"url": result.get('url'),
                                          "title": result.get('title'),
                                          "duration": duration,
                                          "points": points,
                                          "description": result.get('description')})
            scorm_steps.append(_step)

        scorm_steps = self._merge_scorm_steps(scorm_steps, course_title)
        return course_title, scorm_steps

    @staticmethod
    def _merge_scorm_steps(scorm_steps, course_title):
        new_scorm_steps = [{
            "step_title": course_title,
            "step_order": 1,
            "contents": []
        }]

        for step in scorm_steps:
            if len(step['contents']) > 1:
                return scorm_steps

            new_scorm_steps[0]["contents"] = new_scorm_steps[0]["contents"] + step['contents']

        return new_scorm_steps

    def _scorm_activities(self, activities_contents, activity, full_path):
        if not isinstance(activities_contents, list):
            activities_contents = [activities_contents]

        sco_href = [x for x in activities_contents if x['@identifier'] == activity.get('@identifierref')][0]
        url = f'{self._uploader.base_public}/{self._uploader.bucket_name}/scorm/{full_path}/{sco_href.get("@href")}'
        return {
            "url": url,
            "title": activity.get('title') or 'title undefined',
            "description": activity.get('@identifierref')
        }
