from analyze.abc_content import <PERSON><PERSON>zeContent

from analyze.files import AnalyzeVideoAudio, AnalyzeMsOffice, AnalyzePDF, AnalyzeImage  # noqa: F401
from analyze.links import AnalyzeVideoAudioLink, YoutubeVideoAnalyzer, AnalyzeVimeo, AnalyzeSoundcloud, AnalyzeGdrive  # noqa: F401
from analyze.html_link_files import AnalyzeHtmlFiles, AnalyzeHtmlLink  # noqa: F401
from analyze.scorm import AnalyzeScorm  # noqa: F401


# pylint: disable=empty-docstring
class Analyzer:
    """
    """

    _command = None

    """
    Initialize command.
    """

    def set_analyze_command(self, command: AnalyzeContent):
        if isinstance(command, AnalyzeContent):
            self._command = command

    def process_content(self) -> dict:
        return self._command.execute()
