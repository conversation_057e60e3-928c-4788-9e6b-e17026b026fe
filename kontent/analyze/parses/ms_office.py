import re
import xml.etree.ElementTree as ET
import zipfile
import subprocess

import pandas
import pptx
from xlsx2csv import Xlsx2csv
import csv
import xlrd
import os
import shutil
from random import randint


# pylint: disable=redundant-u-string-prefix
# pylint: disable=consider-using-with
# pylint: disable=invalid-name
def pptx2text(filename):
    text = ""

    prs = pptx.Presentation(filename)

    for slide in prs.slides:
        for shape in slide.shapes:
            if hasattr(shape, "text"):
                text += shape.text

    return text


def doc2text(filename):
    """
    Using antiword command (native from Unix OS), extract text from doc file
    http://www.winfield.demon.nl/

    :param filename: path to file
    :return: text extracted
    """
    command = 'antiword "%(filename)s"' % locals()

    pipe = subprocess.Popen(
        command,
        shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
    )

    stdout, _ = pipe.communicate()
    return stdout.decode("utf-8", "ignore")


def docx2text(docx):
    """
    unzip the docx in memory, parse using xml model and get text (from header, main and footer)

    :param docx: path to file
    :return: text extracted
    """
    text = ""

    zipf = zipfile.ZipFile(docx)
    filelist = zipf.namelist()

    header_xmls = "word/header[0-9]*.xml"
    for f_name in filelist:
        if re.match(header_xmls, f_name):
            text += xml2text(zipf.read(f_name))

    doc_xml = "word/document.xml"
    text += xml2text(zipf.read(doc_xml))

    footer_xmls = "word/footer[0-9]*.xml"

    for f_name in filelist:
        if re.match(footer_xmls, f_name):
            text += xml2text(zipf.read(f_name))

    zipf.close()
    return text.strip()


def xlsx2text(filename) -> str:
    dataframe = pandas.read_excel(filename, engine='openpyxl', index=False)
    dataframe = dataframe.dropna(how="all")
    return dataframe.to_string().replace("NaN", "").replace("NaT", "")


def xlsx2text_with_timeout(filename, result_list):
    new_dir_files = filename.replace(".xlsx", "") + str(randint(0, 10))
    Xlsx2csv(filename, outputencoding="utf-8").convert(new_dir_files, sheetid=0)
    output = "\n"
    for filename_csv in os.listdir(new_dir_files):
        if filename_csv.endswith(".csv"):
            with open(
                os.path.join(new_dir_files, filename_csv),
                mode="r",
                newline="",
                encoding="utf-8",
            ) as file:
                reader = csv.reader(file)
                for row in reader:
                    new_output = []
                    for cell in row:
                        new_output.append(cell)
                    if new_output:
                        output += " ".join(new_output)
    shutil.rmtree(new_dir_files)
    result_list[0] = output
    return str(output)


def xls2text(filename):
    workbook = xlrd.open_workbook(filename)
    sheets_name = workbook.sheet_names()
    output = "\n"
    for names in sheets_name:
        worksheet = workbook.sheet_by_name(names)
        num_rows = worksheet.nrows
        num_cells = worksheet.ncols

        for curr_row in range(num_rows):
            worksheet.row(curr_row)
            new_output = []
            for index_col in range(num_cells):
                value = worksheet.cell_value(curr_row, index_col)
                if value:
                    if isinstance(value, (int, float)):
                        value = str(value)
                    new_output.append(value)
            if new_output:
                output += " ".join(new_output) + "\n"
    return output


def qn(tag):
    """
    Stands for 'qualified name', a utility function to turn a namespace
    prefixed tag name into a Clark-notation qualified tag name for lxml.

    Source: https://github.com/python-openxml/python-docx/
    """
    nsmap = {"w": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}

    prefix, tagroot = tag.split(":")
    uri = nsmap[prefix]
    return "{{{}}}{}".format(uri, tagroot)


def xml2text(xml):
    """
    A string representing the textual content of this run, with content
    child elements like ``<w:tab/>`` translated to their Python
    equivalent.

    Adapted from: https://github.com/python-openxml/python-docx/
    """
    text = ""
    root = ET.fromstring(xml)
    for child in root.iter():
        if child.tag == qn("w:t"):
            t_text = child.text
            text += t_text if t_text is not None else ""
        elif child.tag == qn("w:tab"):
            text += "\t"
        elif child.tag in (qn("w:br"), qn("w:cr")):
            text += "\n"
        elif child.tag == qn("w:p"):
            text += "\n\n"
    return text


def ppt_count_slides(pptx_file):
    prs = pptx.Presentation(pptx_file)
    count = 0  # noqa: SIM113
    for _ in prs.slides:
        count += 1
    return count
