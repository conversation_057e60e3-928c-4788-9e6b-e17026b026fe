from __future__ import print_function
from __future__ import unicode_literals
from uuid import uuid4
import yt_dlp
from config.settings import BASE_DIR


def download_audio(url):
    """
    Download audio from youtube, vimeo and soundcloud

    :param url: link to external source
    :return: filename (with path) and metadata from link
    """

    filename = '{}/{}'.format(BASE_DIR + '/temp', str(uuid4()))
    ydl_opts = {
        'format': 'bestaudio/best',
        'outtmpl': '{}'.format(filename),
        'postprocessors': [{
            'key': 'FFmpegExtractAudio',
            'preferredcodec': 'mp3',
            'preferredquality': '192',
        }],
    }

    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        ydl.download([url])

    return filename + '.mp3'


def download_pdf(url):
    """
    Download pdf from youtube, vimeo and soundcloud

    :param url: link to external source
    :return: filename (with path) and metadata from link
    """

    filename = '{}/{}.mp3'.format(BASE_DIR + '/temp', str(uuid4()))
    ydl_opts = {
        'format': 'bestaudio/best',
        'outtmpl': '{}'.format(filename),
        'postprocessors': [{
            'key': 'FFmpegExtractAudio',
            'preferredcodec': 'mp3',
            'preferredquality': '192',
        }],
    }

    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        ydl.download([url])

    return filename
