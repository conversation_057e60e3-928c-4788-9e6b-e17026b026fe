from typing import Optional

from pdfminer.pdfinterp import PDFResourceManager, PDFPageInterpreter
from pdfminer.converter import TextConverter
from pdfminer.layout import LAParams
from pdfminer.pdfpage import PDFPage


try:
    from StringIO import StringIO
except ImportError:
    from io import StringIO


# pylint: disable=consider-using-with
def pdf2text(file_name: str, count_pages: Optional[int] = None) -> str:
    output = StringIO()
    manager = PDFResourceManager()
    converter = TextConverter(manager, output, laparams=LAParams())
    interpreter = PDFPageInterpreter(manager, converter)

    infile = open(file_name, 'rb')  # noqa: SIM115

    for page in PDFPage.get_pages(infile, maxpages=count_pages):
        interpreter.process_page(page)

    infile.close()
    converter.close()
    text = output.getvalue()
    output.close()
    return text
