# pylint: disable=cyclic-import
import zipfile
import os
from django_injector import inject

from constants import TOTAL_SECONDS_IN_A_MINUTE
from di import Container
from custom.keeps_exception_handler import KeepsBadRequestError
from analyze import AnalyzeContent


# pylint: disable=too-many-instance-attributes
# pylint: disable=too-many-arguments
# pylint: disable=no-value-for-parameter
# pylint: disable=consider-using-with
class AnalyzeHtmlFiles(AnalyzeContent):

    @inject
    def __init__(self,
                 container: Container,
                 file_path: str,
                 file_name: str,
                 index_file: str,
                 duration: int,
                 points_rules: int,
                 points_quantity: int,
                 workspace_id: str = None
                 ) -> None:
        super().__init__()
        self._file_to_analyze = file_path
        self._file_name = file_name
        self._index_file = index_file
        self._duration = duration
        self._points_rules = points_rules
        self._points_quantity = points_quantity
        self._uploader = container.aws_s3_client()
        self._webhook_logger = container.webhook_logger
        self._workspace_id = workspace_id

    def execute(self) -> dict:
        """
        :return dict(upload, duration, points, transcribe_job):
        """
        try:
            allowed_index = ['index.html', 'home.html', 'genially.html']
            bucket = self._uploader.bucket_name
            prefix = "html"

            ziped_file = zipfile.ZipFile(self._file_to_analyze)
            index_file = [s for s in ziped_file.namelist() if s in allowed_index]

            if len(index_file) == 0:
                os.remove(self._file_to_analyze)
                raise KeepsBadRequestError(
                    i18n='index_content_not_found',
                    detail='index content in zip file not found. Required: index.html, home.html or genially.html'
                )
            filename = index_file[0]

            self._uploader.send_file(file_name=f'{self._file_name}.zip',
                                     file_path=self._file_to_analyze, bucket=self._uploader.bucket_name,
                                     sub_folder='html', workspace_id=self._workspace_id)

            if self._index_file is None and filename:
                self._index_file = filename

            url = f'{self._uploader.base_public}/{bucket}/{prefix}/{self._file_name}/{self._index_file}'

            if not self._duration:
                # todo: calculation based on HTML text
                self._duration = TOTAL_SECONDS_IN_A_MINUTE

            points = self.compute_points_by_duration(self._duration)

            os.remove(self._file_to_analyze)

            return {
                "url": url,
                "duration": self._duration,
                "points": points,
                "analyzed": True
            }

        except Exception as e:
            self._object = self._file_to_analyze
            self.logger(method="Failure to upload and unzip HTML", error=str(e))
            os.remove(self._file_to_analyze)
            raise KeepsBadRequestError(i18n='error_to_upload_and_unzip_html', detail="Error to upload HTML ZIP file") from e


class AnalyzeHtmlLink(AnalyzeContent):

    @inject
    def __init__(self,
                 container: Container,
                 points_rules: int,
                 points_quantity: int,
                 url: str,
                 duration: int) -> None:
        super().__init__()
        self._url = url
        self._duration = duration
        self._points_rules = points_rules
        self._points_quantity = points_quantity
        self._uploader = container.aws_s3_client()
        self._webhook_logger = container.webhook_logger

    def execute(self) -> dict:
        """
        :return dict(upload, duration, points, transcribe_job):
        """
        # todo: analyze text (transcript, tags, summary) from HTML link

        if not self._duration:
            # todo: calculation based on HTML text
            self._duration = TOTAL_SECONDS_IN_A_MINUTE

        points = self.compute_points_by_duration(self._duration)

        return {
            "url": self._url,
            "duration": self._duration,
            "points": points,
            "analyzed": True
        }
