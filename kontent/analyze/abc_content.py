from abc import ABC, abstractmethod
from math import ceil

from django_injector import inject
from config.settings import TRANSCRIBE_WORDS_PER_MINUTE
from constants import TOTAL_SECONDS_IN_A_MINUTE
from di import Container


class AnalyzeContent(ABC):
    """
    Analyze Content Command interface
    """

    @inject
    def __init__(
        self,
        container: Container,
        workspace_id: str = None,
        points_rules: int = 1,
        points_quantity: int = 1
    ) -> None:
        self._comprehend_client = container.aws_comprehend
        self._summary_client = container.summarize
        self._webhook_logger = container.webhook_logger
        self._object = None
        self._reading_speed_words_per_minute = int(TRANSCRIBE_WORDS_PER_MINUTE)
        self._workspace_id = workspace_id
        self._points_rules = points_rules
        self._points_quantity = points_quantity

    @abstractmethod
    def execute(self) -> dict:
        pass

    def tag(self, text) -> tuple:
        """
        :param text:
        :return keys, entities, language:
        """
        key_tags = None
        entities_tags = None
        language = None

        text = self._short(text)
        if len(text.replace(" ", "")) == 0:
            return key_tags, entities_tags, language

        try:
            language = self._comprehend_client.dominant_language(text)
            language = language['Languages'][0]['LanguageCode']
            key_phrases = self._comprehend_client.key_phrases(text, language)
            entities = self._comprehend_client.entities(text, language)
            key_tags = self._comprehend_client.generate_tags(key_phrases)
            entities_tags = self._comprehend_client.generate_tags(entities)

        except Exception as error:
            self.logger('Tagging', str(error))

        return key_tags, entities_tags, language

    def summary(self, text: str, language: str, sentence_size: int = 4) -> str:
        """
        :param text:
        :param language:
        :param sentence_size:
        :return summarized_text:
        """
        summarized_text = None
        try:
            default_language = 'portuguese'
            language_map = {
                "portuguese": ['pt-BR', 'pt', 'pt-br'],
                "english": ['us', 'en-us', 'en-US', 'en'],
                "spanish": ['es', 'es-AR']
            }
            lang = [x for x in language_map if language in language_map[x]]
            if len(lang) > 0:
                default_language = lang[0]

            summarized_text = self._summary_client.text(
                text=text, language=default_language, sentence_size=sentence_size
            )
        except Exception as error:
            self.logger("Summarizing", str(error))

        return summarized_text

    def logger(self, method: str, error: str) -> None:
        self._webhook_logger.emit_short_message(method, error)

    def text_time_duration(self, text: str) -> int:
        duration_in_minutes = self.count_words(text) / self._reading_speed_words_per_minute
        duration_in_seconds = ceil(duration_in_minutes * TOTAL_SECONDS_IN_A_MINUTE)
        return duration_in_seconds

    def compute_points_by_duration(self, duration_in_seconds: int) -> int:
        minutes = duration_in_seconds / TOTAL_SECONDS_IN_A_MINUTE
        points = ceil(minutes / self._points_quantity * self._points_rules)
        return points

    def compute_points_by_text(self, text: str) -> int:
        return ceil((self.count_words(text) / self._points_quantity) * self._points_rules)

    @staticmethod
    def count_words(text: str) -> int:
        return len(text.split())

    @staticmethod
    def _short(text: str) -> str:
        words = text.split()[:8000]
        shorted = ' '.join(words)
        return shorted
