# -*- coding: utf-8 -*-
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', "config.settings")
django.setup()

from learn_content.models.documents.learn_content_doc import LearnContentDocument

contents = ["9f810fd9-38e9-4d24-a40b-6da8c3b900b2",
"11a9b3db-e4d1-4b01-bc32-483ce2ffb78e",
"137bb121-02ff-4336-adc2-0bb769bfd6f0",
"4fb8c450-d273-4616-8011-ec84f9409fd0",
"807b1cb2-eeff-466a-b91e-09f161ca8075",
"b575e0cb-1b14-4cf0-a498-96d6423b9ebc",
"ba7acdc5-952c-48bb-8a6e-8bb638d7f191",
"bb0172e8-d385-49db-b443-d58398c31310",
"c2736b2d-f867-4065-a520-e9ab71ad6fa7",
"c3cfec6e-d168-4fda-9404-fa2b8176bb3c",
"e76cc70d-6128-44bf-9560-7bf960a56cfa",
"5768e594-64a2-4c4e-b589-6786772f6ef7",
"8e9d2257-302b-4a09-a490-1a8bca868b23",
"86c258f0-5d96-4cb5-aabe-66d2ae671407",
"244940b7-b2eb-4969-b1ba-a3c4f35fc268",
"4eda8a0c-ffc3-409e-a43d-055971c3d3d9",
"56e93ee0-e113-4780-bac4-cef2c9e1e33c",
"b5a1917f-a250-46d1-95e8-9300bb4e2204",
"a17eccaa-83af-4197-8222-fa433f3cf889",
"b3bd91ca-48cd-4f3a-b6a3-2bc2410c6e9b",
"30cc6cce-bbeb-4342-9607-8dbacd5be32e",
"dbee1e50-d1eb-42e3-b55c-46125bd2e1a6",
"d0036e86-9c9b-44f0-900a-6ca9d7fea5e4",
"4253f682-4c3f-4ae4-8c2b-7e4f40d2d3dd",
"3d2d2a75-270b-43be-82fe-91f396f820a4",
"d3e1c9a1-036d-41ea-95c9-30466485f288",
"fb09720d-8149-4c35-aa55-58f2e0e815ed",
"88a6f5b0-9b2d-461d-94a5-cbdd4c909c27",
"bbf082d2-f90f-4034-9743-800c6f17d861",
"59482db0-9b39-4449-a277-b15b0846b798",
"b76e7407-8d9b-45b3-8b5b-747f07f9b03f",
"6f25d28b-a537-41f4-9e5c-6dcf93d57669",
"a849627b-9c21-4139-aa39-442c84c2423d",
"de6e9e7d-c0cb-4166-88f8-c29c3d102718",
"09f577c8-5f5b-4556-8a28-efc971b83ad9",
"13f7e6ac-12a8-458b-a0ac-34d9b93ab62c",
"18144194-a517-40d9-803c-679f1118f82a",
"54f503dd-9d75-4af6-90c6-87f4134e8113",
"b05349a5-8edf-4d46-aeea-4e3ca7d832f2",
"b62b7b32-cbb7-482b-88c5-51491a75bf1d",
"d6f4f6fb-4208-40ea-9cd3-fd7737e36414",
"eb28e2e9-b15b-4286-a0d1-d9f123751b00",
"f46af0d7-3ef6-41fd-9ea3-ddb6dacce79b",
"81440b93-235d-4ea3-8288-545b1dc5f751",
"bdbaf413-2718-4846-9cab-9de6396cdc53",
"a066efb1-50cd-4a07-99fb-c765856f54e4",
"a225e3b8-836c-4f5b-8ba6-be9020d0d698",
"895f1248-5913-4da4-9d4d-e0547ec2942f",
"9fa0afb9-bb7e-488e-89aa-84318f045c9e",
"032c06bb-79ea-4561-914e-bca1e2d5fe4d",
"cc7d7482-d7e3-45b4-b968-5422046bffb0",
"f9716673-840f-41b8-86da-38da62b33dfa",
"018eab78-7d25-43e0-badc-fc9da79925e2",
"1132818c-7a32-4c46-a9d6-46740d348685",
"1ccb9ac3-c370-443b-ad04-3a515a74374a",
"24aa8f86-4ce7-4f1b-9c64-cf8d823223c1",
"58998b7c-8cf7-4a8d-a4b1-6c6e5069798e",
"5bbad478-b010-4046-ba9a-125b72b4002e",
"8c801c45-1f2e-473f-ba06-34b2999adb07",
"92e80e21-4f49-4709-be35-80c9b2266dd8",
"97de6953-6543-4eb1-bb6d-a5d837f4ad64",
"9f8c453c-60cc-4131-881b-5e7ac3946b4a",
"c02c3377-e2bf-400d-a3b0-7a15ce63e02e",
"dbe6603f-e175-4543-9cca-4b57dfb8cdbf",
"f0285bf6-8bea-4a46-842c-8a6fcfed1fc2",
"f8cef73c-7ffc-4929-9956-bd91e35a5079",
"ffd6aaab-f937-4dca-91b7-faf1ee960a4a",
"01ec799c-71a3-4c19-af36-b6612b09f8ea",
"0b09df46-d6fe-4705-80e8-336c8ff9a610",
"1c039e2a-38f7-4030-857c-6bd09a7e94af",
"2a757b8a-19c6-4912-95c3-b7abd2fadeae",
"3544202f-fcf6-4c80-a395-2bd1fc9cc4e1",
"3fe77398-71d1-44a9-ac81-66272b5639f4",
"4ba5cb89-13b7-44ec-a6ae-156c44dd395b",
"66b075db-ee39-42d7-8ade-79a1368eb1ca",
"6ed6f255-ac25-4ad5-ba30-b090b0a6e281",
"75b428a7-d59c-4636-8721-8b9380572524",
"898b7148-98a9-41fc-aaa5-d03ac60d80ba",
"8e494249-aea5-4a3c-9d01-b7acf08dc6f8",
"932ca207-415e-450e-860a-15c49bc4fa6b",
"9e087bf5-d1be-4a3d-928b-9a6839325fa1",
"9eaf03b7-b8e8-42e4-b055-49ecf7f02deb",
"a1c95a70-be32-4dfe-b7da-7700c317f018",
"a2e9bfc0-bccf-47bb-8995-2e625cd85dea",
"a6871955-6f78-4481-b8d7-40ec2abae568",
"b5f22040-80c7-4bc4-a978-03da7c1a450f",
"b759b5e3-d7b1-4440-850a-29bded54dd1f",
"b8b2620a-f0d0-43f9-a169-c8036e9f281e",
"c1a4f405-8e0e-4204-af0b-5e3110b0bfaa",
"d0b03764-3a9e-4dc7-bf4e-f0ad0eb9c83b",
"d655d28a-8ea8-494f-be1f-fc9b1619290d",
"e0133fd6-7f96-4277-a0f3-8158a252b380",
"ec755b10-6d98-4dde-a7b1-54a2a65a57b3",
"f9d68d89-8e94-4ada-a6cc-4906258cf7ca",
"fe1fa417-4eac-4c44-a610-26ca0a194ddc",
"8834b304-40f6-426a-ab2b-c81131efbeb1",
"20433027-33b0-4281-aa26-7c51d1e89957",
"7f76543a-ac13-46d4-9a2c-d1e38abdd718",
"11ae1f14-f376-4528-a724-b19204945366",
"a95cc9ca-b1ba-4933-abd7-1d314b690cbe",
"0339b413-d084-47bc-a819-0e8a72ae9880",
"07ceba8c-27f6-41b6-a2b6-b5d5880dc578",
"c799e5cc-c9d3-4bb7-9373-63ecb0dbbe7e",
"f87f6b9f-fd26-4e65-930c-c55ae39ec042",
"d52740c7-587e-4d43-bb8a-f3ec100719f2",
"dc935a5f-3a63-4802-9dc1-e3d69a813ad4",
"e426d4e5-694a-47dc-afa9-95020bec5c67",
"8b6fbe4c-a95c-416f-a00a-3f4e47c5a03e",
"49e24b42-df88-4ace-80d0-89ec940824e0",
"086c23c9-4154-4359-acfc-c6ee35fee51b",
"37985b9f-26e6-4a87-9b3f-cf1070fc07ea",
"f992b459-6ff1-47f3-b544-b078336877af",
"8dfc9c5f-1cc7-4054-ba28-14053ffe804c",
"a311c3a7-db0c-4aed-ac60-c3ade2852550",
"f596bcb0-546a-48a8-9e7e-d9dfc5345628",
"86f88579-f3f2-4e14-89f6-5861d5a11827",
"8c429ba8-1b4c-4772-880a-86d32c1e0a72",
"314c20e8-7e4a-4db0-9404-54b1f21b8d2e",
"6360bdc8-3822-447b-b230-893661865017",
"94e28fd8-f415-404e-8149-e3d102fd86da",
"b263eefd-d466-4373-9f6f-6f10d905e21b",
"cdb1e833-59ee-4d99-80a3-031232682ad8"]

# print("id", "duration", "points", "analyzed/error")
#
# for content in contents:
#     try:
#         document = LearnContentDocument.get(id=content)
#         print(content, document.duration, document.points, document.analyzed)
#     except Exception as e:
#         print(content, "error", "error", str(e))

document = LearnContentDocument.get("f596bcb0-546a-48a8-9e7e-d9dfc5345628")
document.analyzed = True
document.duration = 20 * 60
document.save()