# -*- coding: utf-8 -*-
import os
import shutil
from uuid import uuid4
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', "config.settings")
django.setup()

from analyze import (<PERSON>lyzer,
                     AnalyzePDF,
                     AnalyzeVideoAudio,
                     AnalyzeMsOffice,
                     AnalyzeImage,
                     YoutubeVideoAnalyzer,
                     AnalyzeVimeo,
                     AnalyzeSoundcloud,
                     AnalyzeHtmlFiles,
                     AnalyzeGdrive,
                     AnalyzeHtmlLink,
                     AnalyzeScorm)

base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
audio_video_local = f'{base_dir}/analyze/tests/files/sample.mp3'
audio_m4a = f'{base_dir}/analyze/tests/files/sample.m4a'
ms_office_doc_local = f'{base_dir}/analyze/tests/files/sample_large.docx'
ms_office_ppt_local = f'{base_dir}/analyze/tests/files/sample.pptx'
ms_office_xls_local = f'{base_dir}/analyze/tests/files/sample.xlsx'
pdf_local = f'{base_dir}/analyze/tests/files/sample.pdf'
png_local = f'{base_dir}/analyze/tests/files/sample.png'

youtube_url = 'https://www.youtube.com/watch?v=GoWNpXFcubc'
vimeo_url = 'https://vimeo.com/225845232'
soundcloud_url = 'https://soundcloud.com/leslie-arjon-rodriguez/micro-podcast'
gdrive_doc = 'https://docs.google.com/document/d/1LDoJMRpBTzqcaQ1b79Cx9cOHM6do2WlD/edit'
gdrive_xls = 'https://docs.google.com/spreadsheets/d/1YQChwayWKkwP7S3iO6Bm5kD7mytf-MBVDOZYQ28mSHA/edit?usp=sharing'

html_zip_local = f'{base_dir}/analyze/tests/files/sample_genially.zip'
html_genially_link = 'https://view.genial.ly/603e8f286829850d2f1dab82/learning-experience-didactic-unit-elegant-didactic-unit'

scorm_file = f'{base_dir}/analyze/tests/files/sample_scorm.zip'


def copy_file_temp(file_to_copy):
    extension = file_to_copy.split('/')[-1].split('.')[1]
    new_file = f'{base_dir}/analyze/tests/files/temp-process.{extension}'
    shutil.copyfile(file_to_copy, new_file)
    return new_file


def print_return(response, file_type):
    print(f"######### {file_type} ######")
    print(f"\nurl: {response.get('url', None)}"),
    print(f"\nduration: {response.get('duration', None)}"),
    print(f"\npoints: {response.get('points', None)}"),
    print(f"\ntranscript: {response.get('transcript', None)}"),
    print(f"\ntags: {response.get('tags', None)}"),
    print(f"\nentities: {response.get('entities', None)}"),
    print(f"\nlanguage: {response.get('language', None)}")
    print(f"\nanalyzed: {response.get('analyzed', None)}")


def print_return_scorm(response, file_type):
    print(f"######### {file_type} ######")
    print(f"\ncourse title: {response.get('course_title', None)}"),
    print(f"\ncourse steps: {response.get('course_steps', None)}"),


if __name__ == "__main__":
    """
    The client code can parameterize an invoker with any commands.
    """

    invoker = Analyzer()

    invoker.set_analyze_command(AnalyzeGdrive(
        points_rules=10,
        points_quantity=1,
        url=gdrive_doc
    ))
    gdrive_doc = invoker.process_content()
    print_return(gdrive_doc, "Gdrive Documents")

    invoker.set_analyze_command(AnalyzeGdrive(
        points_rules=10,
        points_quantity=1,
        url=gdrive_xls
    ))
    gdrive_xls = invoker.process_content()
    print_return(gdrive_xls, "Gdrive Documents")
