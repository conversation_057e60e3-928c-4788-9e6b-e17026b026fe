# -*- coding: utf-8 -*-
import os
import django
from django.db.models import Count

os.environ.setdefault('DJANGO_SETTINGS_MODULE', "config.settings")
django.setup()

from assessment_database.models import Answer

user_answers = Answer.objects.values('question', 'user_id', 'enrollment_id').order_by().annotate(Count('question'), Count('user_id'), Count('enrollment_id'))
count = 0

print(f'id,enrollment,question,user,count_duplicated,count_deleted')

for user_answer in user_answers:

    if user_answer['question__count'] > 1:
        answers = Answer.objects.filter(question=user_answer['question'], user_id=user_answer['user_id']).order_by('question')

        to_delete = 1
        enrollment_id = 'oi'
        for q in answers:
            enrollment_id = q.enrollment_id
            if to_delete < user_answer['question__count']:
                print(f'{q.id},{enrollment_id},{q.question_id},{user_answer["user_id"]},{user_answer["question__count"]},{to_delete},deleted')
                q.delete()
                to_delete += 1
            else:
                print(f'{q.id},{enrollment_id},{q.question_id},{user_answer["user_id"]},{user_answer["question__count"]},{to_delete},keep')
        count += 1

print(count)
