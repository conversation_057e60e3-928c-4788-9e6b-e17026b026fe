import logging

import os
import django
import nltk

from analyze import AnalyzeHtmlFiles, AnalyzeScorm, AnalyzeHtmlLink

nltk.download('punkt')
from elasticsearch.exceptions import NotFoundError
from tasks.content_analyzers.analyze_content import analyze_content

os.environ.setdefault('DJANGO_SETTINGS_MODULE', "config.settings")
django.setup()


from config.settings import BASE_DIR
from learn_content.models import LearnContent, ContentPointRule, LearnContentDocument

SCRIPT_LOG_FOLDER = f"{BASE_DIR}/script/logs"
SCRIPT_LOG_FORMAT = "%(asctime)s:%(levelname)s:%(message)s"


html_point_rules = ContentPointRule.objects.get(content_type__name="HTML")
html_file_point_rules = ContentPointRule.objects.get(content_type__name="HTML File")
presentation_point_rules = ContentPointRule.objects.get(content_type__name="Presentation")
text_point_rules = ContentPointRule.objects.get(content_type__name="Text")
spreadsheet_point_rules = ContentPointRule.objects.get(content_type__name="Spreadsheet")
video_point_rules = ContentPointRule.objects.get(content_type__name="Video")
pdf_point_rules = ContentPointRule.objects.get(content_type__name="PDF")
image_point_rules = ContentPointRule.objects.get(content_type__name="Image")
scorm_point_rules = ContentPointRule.objects.get(content_type__name="SCORM")

logging.basicConfig(
    filename=f"{SCRIPT_LOG_FOLDER}/reprocess-content-duration-and-points-PRD.log",
    level=logging.INFO,
    format=SCRIPT_LOG_FORMAT,
)
logger = logging.getLogger()

def run():
    results = []
    logger.info("starting script...")
    contents = LearnContent.objects.filter().exclude(content_type_id=pdf_point_rules.content_type_id).select_related("content_type").order_by("-created_date")
    total = len(contents)
    processed = 0
    logger.info(f"total of contents {total}")

    old_log_string =  ""
    old_log = f"{SCRIPT_LOG_FOLDER}/reprocess-content-duration-and-points-PRD.log"
    if old_log:
        with open(old_log, 'r') as f:
            for line in f:
                old_log_string += str(line)

    for content in contents:
        if f"content reprocessed: {content.id}" in old_log_string:
            processed += 1
            logger.info(f"PROGRESS: {(processed / total) * 100}%")
            continue
        if f"content not found in elastic: {content.id}" in old_log_string:
            processed += 1
            logger.info(f"PROGRESS: {(processed / total) * 100}%")
            continue
        if f"unable to retrieve document details: {content.id}" in old_log_string:
            processed += 1
            logger.info(f"PROGRESS: {(processed / total) * 100}%")
            continue
        try:
            process_content(content)
            logger.info(f"content reprocessed: {content.id}")
        except NotFoundError as error:
            logger.info(f"content not found in elastic: {content.id} . {error}")
        except AttributeError as error:
            if "'NoneType' object has no attribute 'id'" in str(error):
                logger.info(f"unable to retrieve document details: {content.id} . {error}")
            else:
                logger.info(f"error content: {content.id}. {error}")
        except Exception as error:
            logger.info(f"error content: {content.id}. {error}")

        processed += 1
        logger.info(f"PROGRESS: {(processed/total)*100}%")

def process_content(content: LearnContent):
    document = LearnContentDocument().get(content.id)
    if not document.analyzed:
        logger.info(f"skipping content: {content.id}, not analyzed")
        return

    result = {}
    if content.content_type.id == html_point_rules.content_type_id:
        duration = document.duration or 10
        analyze_html_files = AnalyzeHtmlLink(
            file_path="",
            file_name="",
            index_file="",
            duration=duration,
            points_quantity=html_point_rules.quantity,
            points_rules=html_point_rules.points,
            workspace_id="",
        )
        points = max(analyze_html_files.compute_points_by_duration(duration), 1)
        result = {"points": points }

    if str(content.content_type.id) == str(scorm_point_rules.content_type_id):
        duration = document.duration or 10
        analyze_scorm_files = AnalyzeScorm(
            file_path="",
            file_name="",
            duration=duration,
            points_quantity=scorm_point_rules.quantity,
            points_rules=scorm_point_rules.points
        )
        points = max(analyze_scorm_files.compute_points_by_duration(duration), 1)
        result = {"points": points }

    if content.content_type.id == html_file_point_rules.content_type_id:
        duration = document.duration or 10
        analyze_html_files = AnalyzeHtmlFiles(
            file_path="",
            file_name="",
            index_file="",
            duration=duration,
            points_quantity=html_point_rules.quantity,
            points_rules=html_point_rules.points,
            workspace_id="",
        )
        points = max(analyze_html_files.compute_points_by_duration(duration), 1)
        result = {"points": points }


    if content.content_type.id in [
        presentation_point_rules.content_type_id,
        text_point_rules.content_type_id,
        spreadsheet_point_rules.content_type_id,
        #pdf_point_rules.content_type_id, Está ocorrendo um problema para extrair o texto do pdf
        image_point_rules.content_type_id
    ]:
        analyze_content(content.id)
        return

    if content.content_type.id == video_point_rules.content_type_id:
        points = round(int((document.duration / 60) / video_point_rules.quantity + 0.6)) * video_point_rules.points
        result = {"points": points or 2}

    if result.get("points") != document.points and result:
        logger.info(f"update information: content ({content.id}): 'old_points': {document.points} | {result}")

    # TODO: without result. by pass
    if not result:
        logger.info(f"skipping content: {content.id}, content type unable to reprocess")
        return content
    
    content.save()
    document.points = result.get("points") or 2
    document.save()
    return content


if __name__ == '__main__':
    run()