import boto3
import os

from config.settings import AWS_BUCKET_NAME

DOWNLOAD_PATH = "downloads"

# Allowed video file extensions
VIDEO_EXTENSIONS = {".mp4", ".mkv", ".avi", ".mov", ".webm"}

# Initialize S3 client
s3 = boto3.client("s3")


def list_video_files(bucket_name):
    """List all video files in the specified S3 bucket."""
    video_files = []
    response = s3.list_objects_v2(Bucket=bucket_name)

    if "Contents" in response:
        for obj in response["Contents"]:
            key = obj["Key"]
            if any(key.lower().endswith(ext) for ext in VIDEO_EXTENSIONS):
                video_files.append(key)

    return video_files


def download_file(bucket_name, key, download_path):
    """Download a file from S3 to the local directory."""
    local_filename = os.path.join(download_path, os.path.basename(key))
    os.makedirs(download_path, exist_ok=True)

    print(f"Downloading {key} to {local_filename}...")
    s3.download_file(bucket_name, key, local_filename)
    print(f"Downloaded: {local_filename}")


def main():
    """Main function to list and download video files."""
    print(f"Listing video files in bucket: {AWS_BUCKET_NAME}")
    video_files = list_video_files(AWS_BUCKET_NAME)

    if not video_files:
        print("No video files found in the bucket.")
        return

    print(f"Found {len(video_files)} video files.")

    for video in video_files:
        download_file(AWS_BUCKET_NAME, video, DOWNLOAD_PATH)


if __name__ == "__main__":
    main()
