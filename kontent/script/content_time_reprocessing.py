import os
import re
import urllib
from uuid import uuid4

# import moviepy.editor as mp
from urllib.parse import urlparse
from six.moves import urllib

# from utils.vimeo_api_client import VimeoInfoExtractor
import django
from config import settings

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from analyze.parses import pdf
from learn_content.models import ContentPointRule, LearnContentDocument, LearnContent, ContentType
from di import Container
from custom import DISCORD_WEBHOOK

from analyze import (
    YoutubeVideoAnalyzer,
)


class ProcessingDuration:
    def __init__(self):
        self.regex_youtube = "^(http(s)??\:\/\/)?(www\.)?((youtube\.com\/watch\?v=)|(youtu.be\/))([^&=%\?]{11})"
        self.regex_vimeo = "^(http|https)?:\/\/(www\.)?vimeo.com\/(?:channels\/(?:\w+\/)?|groups\/([^\/]*)\/videos\/|)(\d+)(?:|\/\?)"
        self.regex_sound_cloud = "^(https?:\/\/)?(www.)?(m\.)?soundcloud\.com\/[\w\-\.]+(\/)+[\w\-\.]+/?$"
        self._video_point_rule = ContentPointRule.objects.filter(content_type="569cc389-ac1d-4fa0-9692-f715b475b59b").first()
        self._audio_point_rule = ContentPointRule.objects.filter(content_type="799f766c-a956-4c03-b5aa-bde9ba357de8").first()
        self._pdf_point_rule = ContentPointRule.objects.filter(content_type="0faac34b-2393-4352-8a94-a9ee0659f824").first()
        self._img_point = ContentPointRule.objects.filter(content_type="2284bfce-fdfc-4477-9143-39c380cc653c").first()

    def execute(self):
        count = 0
        contents = LearnContent.objects.filter(
            content_type="569cc389-ac1d-4fa0-9692-f715b475b59b", created_date__gte="2025-01-01", created_date__lte="2025-04-23", url__icontains="youtu.be"
        )
        duration = None
        points = None

        content_type = ContentType.objects.filter(extensions__icontains="youtube").first()
        points_rules = content_type.contentpointrule_set.first()

        print(points_rules.points)
        print(points_rules.quantity)

        for content in contents:
            count = count + 1
            try:
                document = LearnContentDocument.get(id=content.id)
                print(f"Analyzing content id: {content.url}")
                duration, points = self.youtube_soundcloud_vimeo(document.url, points_rules.points, points_rules.quantity)
                print(f"Antes points{document.points} duration:{document.duration} -> Depois points{points} duration:{duration}")

                if duration and points:
                    document.duration = duration
                    document.points = points
                    document.analyzed = True
                    document.save()
                    print(document.url, duration, points)
                    duration, points = None, None
                    print("################################################################")
            except Exception as error:
                print(content.url, error)

        print(f"Completed:{count}")

    def calc_point_by_duration_minutes(self, minutes):
        in_minutes = int(round(minutes / 60 + 0.5))
        points = int(in_minutes / self._video_point_rule.quantity) * self._video_point_rule.points
        return points

    def youtube_soundcloud_vimeo(self, url, points_rules, points_quantity):
        try:
            analizer = YoutubeVideoAnalyzer(url=url, points_rules=points_rules, points_quantity=points_quantity)
            info = analizer.execute()
            duration = info.get("duration")
            points = self.calc_point_by_duration_minutes(duration)
            return duration, points
        except Exception as error:
            DISCORD_WEBHOOK.emit_short_message("Error get content info", error)
            return 10, 1

    def video_s3_hosted(self, url, extension):
        try:
            if extension in ["mp3", "m4a"]:
                duration = mp.AudioFileClip(url).duration
            else:
                duration = mp.VideoFileClip(url).duration

            in_minutes = round(duration / 60 + 0.5)
            points = int((in_minutes / self._video_point_rule.quantity) * self._video_point_rule.points)
            return int(duration), int(points)

        except Exception as error:
            print(error)
            return 120, 1

    def pdf_s3_hosted(self, url):
        file_name = f"{settings.BASE_DIR}/temp/{uuid4()}.pdf"
        urllib.request.urlretrieve(url, file_name)
        transcript = pdf.pdf2text(file_name)
        words = transcript.split()
        duration = int(len(words) / 500 * 60)
        points = int((duration / self._pdf_point_rule.quantity) * self._pdf_point_rule.points)
        os.remove(file_name)
        return duration, points

    def image_s3_hosted(self, url):
        transcript = Container().aws_rekognition.detect_image_text(url)
        if len(transcript) == 0:
            return 10, 1
        words = transcript.split()
        duration = int(len(words) / 500 * 60)
        points = int(duration / self._img_point.quantity * self._img_point.points)
        return duration, points


ProcessingDuration().execute()
