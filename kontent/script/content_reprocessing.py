import os
import django

from tasks.content_analyzers.update_doc import update_elastic_doc

os.environ.setdefault('DJANGO_SETTINGS_MODULE', "config.settings")
django.setup()


from learn_content.models import LearnContent
from tasks.content_analyzers.analyze_content import analyze_content

def run_content_analysis():
    content_to_analyze = LearnContent.objects.filter(analyzed=False)
    print("Found {} content to analyze".format(len(content_to_analyze)))

    for content in content_to_analyze:
        print("Analyzing content: {}".format(content.id))
        content_id = str(content.id)
        if content.content_type.name == "HTML":
            content.analyzed = True
            content.save()
            update_elastic_doc(content.id, {"analyzed": True})
        else:
            analyze_content(content_id)

run_content_analysis()
