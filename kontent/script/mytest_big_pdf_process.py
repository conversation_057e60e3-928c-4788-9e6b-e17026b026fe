import datetime

import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', "config.settings")
django.setup()

from analyze import AnalyzePDF
from di import Container
from learn_content.models import ContentPointRule


KONTENT_URL = os.getenv("AWS3_URL_TO_TEST_BIG_PDF")
def run():
    print(f"{datetime.datetime.now()} | pdf_process")
    content_point = ContentPointRule.objects.filter(content_type_id="0faac34b-2393-4352-8a94-a9ee0659f824").first()
    s3_client = Container().aws_s3_client()
    analyzer = AnalyzePDF(
        file_name=None,
        points_rules=content_point.points,
        points_quantity=content_point.quantity,
        file_path=None
    )
    bucket, key = s3_client.extract_s3_url_bucket_key(KONTENT_URL)
    analyzer.file_to_analyze = s3_client.download(key, bucket)
    print(analyzer.process())

if __name__ == '__main__':
    run()