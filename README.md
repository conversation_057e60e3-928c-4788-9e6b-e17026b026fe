# Kontent API
[![Quality Gate Status](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_keeps-kontent-server_AYmYl5789dESJCsQS4Bs&metric=alert_status&token=9fe0e6adba8a7e854cb38a4b7943221c08acbd56)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_keeps-kontent-server_AYmYl5789dESJCsQS4Bs)
[![Coverage](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_keeps-kontent-server_AYmYl5789dESJCsQS4Bs&metric=coverage&token=9fe0e6adba8a7e854cb38a4b7943221c08acbd56)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_keeps-kontent-server_AYmYl5789dESJCsQS4Bs)
[![Maintainability Rating](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_keeps-kontent-server_AYmYl5789dESJCsQS4Bs&metric=sqale_rating&token=9fe0e6adba8a7e854cb38a4b7943221c08acbd56)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_keeps-kontent-server_AYmYl5789dESJCsQS4Bs)
[![Security Rating](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_keeps-kontent-server_AYmYl5789dESJCsQS4Bs&metric=security_rating&token=9fe0e6adba8a7e854cb38a4b7943221c08acbd56)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_keeps-kontent-server_AYmYl5789dESJCsQS4Bs)
[![Reliability Rating](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_keeps-kontent-server_AYmYl5789dESJCsQS4Bs&metric=reliability_rating&token=9fe0e6adba8a7e854cb38a4b7943221c08acbd56)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_keeps-kontent-server_AYmYl5789dESJCsQS4Bs)
[![Vulnerabilities](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_keeps-kontent-server_AYmYl5789dESJCsQS4Bs&metric=vulnerabilities&token=9fe0e6adba8a7e854cb38a4b7943221c08acbd56)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_keeps-kontent-server_AYmYl5789dESJCsQS4Bs)
[![Bugs](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_keeps-kontent-server_AYmYl5789dESJCsQS4Bs&metric=bugs&token=9fe0e6adba8a7e854cb38a4b7943221c08acbd56)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_keeps-kontent-server_AYmYl5789dESJCsQS4Bs)
[![Lines of Code](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_keeps-kontent-server_AYmYl5789dESJCsQS4Bs&metric=ncloc&token=9fe0e6adba8a7e854cb38a4b7943221c08acbd56)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_keeps-kontent-server_AYmYl5789dESJCsQS4Bs)
![example branch parameter](https://github.com/Keeps-Learn/keeps-kontent-server/actions/workflows/master.yml/badge.svg)

## Executar via Docker Compose
1. Instale o [docker-compose](https://docs.docker.com/compose/install/)
2. Baixe o projeto do repositório: ```<NAME_EMAIL>:Keeps-Learn/keeps-kontent-server.git```<br>
Para clonar o projeto localmente, você precisa adicionar uma chave SSH ao seu perfil no Github.
Caso ainda não tenha feito isso, siga os passos descritos em [Adding a new SSH key to your GitHub account](https://docs.github.com/en/authentication/connecting-to-github-with-ssh/adding-a-new-ssh-key-to-your-github-account)
3. Acesse a pasta ```keeps-kontent-server```
4. Crie um arquivo **.env.dev** contendo as variáveis de ambiente.<br>
*Solicite ao seu team leader um arquivo já configurado.*
5. Execute o comando ```docker-compose up --build```

Verifique a documentação da API no endereço ```http://localhost:8000```

## Executando Análise de Projeto com SonarQube Scanner via Docker

Para realizar a análise de um projeto usando o SonarQube Scanner através de um contêiner Docker, siga as instruções abaixo. Este método é útil para ambientes onde você deseja utilizar o scanner sem precisar instalá-lo localmente.

### Pré-requisitos

1. **Acesso ao SonarQube**: Certifique-se de que você tem uma instância do SonarQube disponível e acessível. Verifique o URL e o projeto previamente configurado.
2. **Token de Autenticação**: Gere um token de autenticação na interface do SonarQube. Isso será necessário para autenticar o scanner.

### Comando para executar a análise

Execute o seguinte comando Docker no diretório raiz do seu projeto:

```bash
docker run --rm \
  -e SONAR_HOST_URL="https://sonar.keepsdev.com" \
  -e SONAR_SCANNER_OPTS="-Dsonar.projectKey=keeps-kontent-server" \
  -e SONAR_TOKEN="TOKEN" \
  -v "${PWD}:/usr/src" \
  sonarsource/sonar-scanner-cli
```
