sonar.projectKey=keeps-kontent-server
sonar.sources=.
sonar.verbose=false
sonar.exclusions = **/*.html,**/.venv/**, **/venv/**, **migrations**, **tests**, **apps.py, **migrations/**, **settings**, **config**, **tests/**, **urls.py, **wsgi/**, **manage.py, **script/**, **__init__.py, **analyze**, **tasks**



sonar.python.version=3
sonar.python.coverage.reportPaths=.reports/coverage/coverage.xml
sonar.python.xunit.reportPath=.reports/xunit/xunit.xml
sonar.python.flake8.reportPaths=.reports/output_flake.txt
sonar.python.pylint.reportPaths=.reports/output_pylint.txt