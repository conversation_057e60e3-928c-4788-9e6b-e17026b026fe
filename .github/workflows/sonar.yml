name: Quality Analysis

on:
  workflow_call:
    secrets:
      SONAR_TOKEN:
        required: true
      SONAR_HOST_URL:
        required: true

jobs:
  sonar:
    name: Sonar
    runs-on: ubuntu-latest
    steps:
      - name: Downloading Source Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Shallow clones should be disabled for a better relevancy of analysis
      
      - name: Downloading Reports
        uses: actions/download-artifact@v4
        with:
          name: reports
          path: .reports 

      - name: Display structure of downloaded files
        run: ls -R

      - name: Running Sonar Scan
        uses: sonarsource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}