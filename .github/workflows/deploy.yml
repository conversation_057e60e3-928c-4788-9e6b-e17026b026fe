name: Deploy Pipeline

env:
  DEPLOYMENTS: deployment/kontent  deployment/kontent-worker
  PROJECT_FOLDER: kontent

on:
  workflow_dispatch:
  push:
    branches:
      - main
      - develop

jobs:
  build-and-deploy:
    name: build-deploy
    runs-on: ubuntu-latest
    steps:
      - name: Download code
        uses: actions/checkout@v4

      - name: Setup Environment Variables
        run: |
          if [ "${{ github.head_ref }}" = "develop" ] || [ "${{ github.ref_name }}" = "develop" ]; then
            echo "NAMESPACE=stage" >> $GITHUB_ENV
          elif [ "${{ github.head_ref }}" = "main" ] || [ "${{ github.ref_name }}" = "main" ]; then
            echo "NAMESPACE=production" >> $GITHUB_ENV
          else
            echo "NAMESPACE=${{ github.head_ref || github.ref_name }}" >> $GITHUB_ENV
          fi

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        if: success()
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_EKS }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_EKS }}
          aws-region: ${{ secrets.AWS_DEFAULT_REGION }}

      - name: Login to Amazon ECR Private
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push docker image to Amazon ECR
        env:
          ECR_REPOSITORY: ${{ secrets.ECR_REPOSITORY }}
        run: |
          IMAGE_TAG_LATEST=$NAMESPACE
          cd $PROJECT_FOLDER
          docker build -f Dockerfile -t "$ECR_REPOSITORY:$IMAGE_TAG_LATEST" .
          docker push "$ECR_REPOSITORY:$IMAGE_TAG_LATEST"

      - name: Deploy EKS
        uses: kodermax/kubectl-aws-eks@master
        env:
          KUBE_CONFIG_DATA: ${{ secrets.KUBE_CONFIG }}
        with:
          args: rollout restart -n $NAMESPACE $DEPLOYMENTS

  release-on-push:
    runs-on: ubuntu-latest
    needs: [build-and-deploy]
    if: github.ref == 'refs/heads/main'
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - uses: rymndhng/release-on-push-action@master
        with:
          bump_version_scheme: minor