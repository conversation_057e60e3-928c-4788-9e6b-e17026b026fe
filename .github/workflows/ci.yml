name: Pi<PERSON>ine (CI)

on:
  workflow_dispatch:
  pull_request:
    branches:
      - main
      - develop
    types: [opened, synchronize, reopened]
jobs:
  tests:
    name: Run tests
    uses: ./.github/workflows/test_and_lint.yml
    secrets: inherit

  sonar:
    name: Quality Analysis
    if: github.ref_name != 'main'
    uses: ./.github/workflows/sonar.yml
    needs: [tests]
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      