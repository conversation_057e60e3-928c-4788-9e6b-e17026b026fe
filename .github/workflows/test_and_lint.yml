name: Unit tests and lint

on:
  workflow_call:
    inputs:
      run-build:
        default: true
        type: boolean
        required: false

jobs:
  unit-tests:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:latest
        env:
          POSTGRES_PORT: 5432
          POSTGRES_DB: kontent_tst
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          DATABASE_NAME: kontent_tst
          DATABASE_HOST: postgres
          DATABASE_USER: postgres
          DATABASE_PASSWORD: postgres

        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-python@v5
        with:
          python-version: "3.8"
          cache: "pip"

      - name: Install dependencies
        run: |
          cd kontent
          python -m pip install pip==24.0
          pip install numpy
          pip install -r requirements.txt

      - name: Running Tests
        shell: bash
        env:
          AWS_TRANSCRIBE_ACCESS_KEY: "${{secrets.AWS_TRANSCRIBE_ACCESS_KEY}}"
          AWS_TRANSCRIBE_SECRET_KEY: "${{secrets.AWS_TRANSCRIBE_SECRET_KEY}}"
          PDFTRON_KEY: "${{secrets.PDFTRON_KEY}}"
          KEEPS_SECRET_TOKEN_INTEGRATION: "${{secrets.KEEPS_SECRET_TOKEN_INTEGRATION}}"
          AWS_TRANSCRIBE_BUCKET: keeps.transcribe
          AWS_TRANSCRIBE_REGION: us-east-1
          DEBUG: True
          ELASTICSEARCH_HOST: keeps.es.us-east-1.aws.found.io
          ELASTICSEARCH_INDEX: kontent-hml
          ELASTICSEARCH_USER: "${{secrets.ELASTICSEARCH_USER}}"
          GOOGLE_APPLICATION_CREDENTIALS:  "${{secrets.GOOGLE_APPLICATION_CREDENTIALS}}"
          ELASTICSEARCH_PASS: "${{secrets.ELASTICSEARCH_PASS}}"
        run: |
          make test-cov
          
      - name: List .reports directory (for debugging)
        run: ls -R .reports

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: reports
          path: .reports/
          include-hidden-files: true

