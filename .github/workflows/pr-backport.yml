name: Pull request backport

on:
  pull_request_target:
    types: [ closed ]
    branches:
      - develop

permissions:
  contents: write
  pull-requests: write

jobs:
  backport:
    name: Backport pull request
    runs-on: ubuntu-latest
    # Don't run on closed unmerged pull requests
    if: github.event.pull_request.merged
    steps:
      - uses: actions/checkout@v4

      - name: Create backport pull requests
        uses: korthout/backport-action@v3
        with:
          label_pattern: ^test-review ([^ ]+)$
          copy_assignees: true
          copy_requested_reviewers: true
          github_token: ${{ secrets.GITHUB_TOKEN }}
          pull_title: "[Backport] ${pull_title}"
