#!/bin/bash

export DATABASE_NAME=kontent_dev_db
export DATABASE_USER=[username]
export DATABASE_PASSWORD=[password]
export DATABASE_HOST=database-stage-keeps.cpd3dmaosiyq.us-east-1.rds.amazonaws.com
export DATABASE_PORT=5432

export ELASTICSEARCH_HOST=https://keeps.es.us-east-1.aws.found.io:9243
export ELASTICSEARCH_USER=elastic
export ELASTICSEARCH_PASS=[password]
export ELASTICSEARCH_INDEX=kontent-test

export DEBUG=True
export ENVIRONMENT=development
export PYTHONPATH="${PYTHONPATH}:${PWD}/kontent/"

# TODO geração do pode demorar um bocado
python kontent/manage.py generate_swagger -o kontent/swagger.json

python kontent/manage.py runserver 0.0.0.0:8000
