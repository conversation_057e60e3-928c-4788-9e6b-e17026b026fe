#!/bin/bash

export DATABASE_USER=postgres
# export DATABASE_HOST=rds-postgres-stage.cpd3dmaosiyq.us-east-1.rds.amazonaws.com
# export DATABASE_PASSWORD=TqW9ypey2IjDDmZVdE3r52ws46OkkP
# export DATABASE_NAME=kontent_dev_db

export DATABASE_HOST=keeps-learn-platform-us-east.cpd3dmaosiyq.us-east-1.rds.amazonaws.com
export DATABASE_PASSWORD=5WsHV7cw2JrvQmqVerB3YwfxXxCXTCX2T3kjmmrsw29JBjWLHP
export DATABASE_NAME=kontent_db
export DATABASE_PORT=5432

export PYTHONPATH=./kontent
export KONTENT_API_URL=https://learning-platform-api.keepsdev.com/kontent
#export KONTENT_API_URL=https://learning-platform-api-stage.keepsdev.com/kontent


export ELASTICSEARCH_HOST=keeps.es.us-east-1.aws.found.io
export ELASTICSEARCH_INDEX=kontent-prod
export ELASTICSEARCH_PASS=Ztx2l2zLkf2DkmQYuAYRR0Ty
export ELASTICSEARCH_USER=elastic

export DISCORD_WEBHOOK=

export DEBUG=True
export ENVIRONMENT=development
export ENVIRONMENT_TEST=True
export SUSPEND_SIGNALS=true

# python konquest/scripts/recalculate_mission_enrollments_performance.py 
# python konquest/scripts/reprocess_mission.py 
# python kontent/script/content_time_reprocessing.py
